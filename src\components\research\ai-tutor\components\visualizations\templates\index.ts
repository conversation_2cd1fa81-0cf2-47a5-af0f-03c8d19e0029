/**
 * Visualization Templates Index
 * Exports all subject-specific visualization templates
 */

export { default as PhysicsSimulationTemplate } from './PhysicsSimulationTemplate';
export { default as MathVisualizationTemplate } from './MathVisualizationTemplate';
export { default as ChemistryVisualizationTemplate } from './ChemistryVisualizationTemplate';

// Template registry for easy access
export const VISUALIZATION_TEMPLATES = {
  physics: PhysicsSimulationTemplate,
  mathematics: MathVisualizationTemplate,
  chemistry: ChemistryVisualizationTemplate,
} as const;

export type VisualizationTemplateType = keyof typeof VISUALIZATION_TEMPLATES;
