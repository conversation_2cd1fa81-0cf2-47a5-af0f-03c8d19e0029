/**
 * Visualization Detection Service
 * Analyzes user questions and automatically determines when visualizations would be beneficial for learning
 */

import { 
  VisualizationDetectionResult, 
  Subject, 
  GradeLevel, 
  DifficultyLevel, 
  VisualizationType 
} from '../types';

interface DetectionKeywords {
  [key: string]: {
    subjects: Subject[];
    visualizationType: VisualizationType;
    confidence: number;
  };
}

interface SubjectKeywords {
  [key: string]: {
    keywords: string[];
    concepts: string[];
    visualizationTriggers: string[];
  };
}

export class VisualizationDetectionService {
  private static instance: VisualizationDetectionService;

  // Keywords that strongly suggest visualization would be helpful
  private readonly VISUALIZATION_TRIGGERS = [
    'how does', 'show me', 'visualize', 'demonstrate', 'illustrate',
    'i don\'t understand', 'explain visually', 'can you show',
    'what does it look like', 'help me see', 'picture this',
    'animate', 'simulate', 'model', 'diagram', 'graph',
    'plot', 'draw', 'sketch', 'display'
  ];

  // Subject-specific keywords and concepts
  private readonly SUBJECT_KEYWORDS: SubjectKeywords = {
    physics: {
      keywords: ['motion', 'force', 'energy', 'wave', 'particle', 'field', 'quantum', 'gravity'],
      concepts: ['projectile motion', 'circular motion', 'oscillation', 'interference', 'electric field', 'magnetic field'],
      visualizationTriggers: ['trajectory', 'path', 'movement', 'vibration', 'propagation']
    },
    mathematics: {
      keywords: ['function', 'equation', 'graph', 'derivative', 'integral', 'matrix', 'vector'],
      concepts: ['calculus', 'algebra', 'geometry', 'trigonometry', 'statistics', 'probability'],
      visualizationTriggers: ['plot', 'curve', 'slope', 'area under curve', 'transformation']
    },
    chemistry: {
      keywords: ['molecule', 'atom', 'bond', 'reaction', 'structure', 'orbital', 'electron'],
      concepts: ['molecular structure', 'chemical bonding', 'reaction mechanism', 'periodic trends'],
      visualizationTriggers: ['3d structure', 'molecular model', 'electron cloud', 'bond formation']
    },
    biology: {
      keywords: ['cell', 'dna', 'protein', 'organism', 'evolution', 'ecosystem', 'anatomy'],
      concepts: ['cell division', 'protein synthesis', 'genetic inheritance', 'natural selection'],
      visualizationTriggers: ['cell structure', 'dna replication', 'protein folding', 'evolutionary tree']
    },
    'computer-science': {
      keywords: ['algorithm', 'data structure', 'network', 'machine learning', 'programming'],
      concepts: ['sorting', 'searching', 'tree traversal', 'neural networks', 'protocols'],
      visualizationTriggers: ['step by step', 'algorithm visualization', 'data flow', 'network topology']
    },
    engineering: {
      keywords: ['circuit', 'system', 'design', 'mechanics', 'signal', 'control'],
      concepts: ['circuit analysis', 'system dynamics', 'signal processing', 'control systems'],
      visualizationTriggers: ['circuit diagram', 'system response', 'signal waveform', 'block diagram']
    },
    economics: {
      keywords: ['market', 'supply', 'demand', 'price', 'equilibrium', 'elasticity'],
      concepts: ['supply and demand', 'market equilibrium', 'price elasticity', 'game theory'],
      visualizationTriggers: ['supply curve', 'demand curve', 'market graph', 'economic model']
    },
    general: {
      keywords: ['concept', 'idea', 'process', 'system', 'relationship'],
      concepts: ['cause and effect', 'comparison', 'sequence', 'hierarchy'],
      visualizationTriggers: ['flowchart', 'diagram', 'timeline', 'comparison chart']
    }
  };

  public static getInstance(): VisualizationDetectionService {
    if (!VisualizationDetectionService.instance) {
      VisualizationDetectionService.instance = new VisualizationDetectionService();
    }
    return VisualizationDetectionService.instance;
  }

  /**
   * Analyzes a user message to determine if visualization would be beneficial
   */
  public async detectVisualizationNeed(
    message: string,
    context: string,
    gradeLevel: GradeLevel,
    conversationHistory: string[] = []
  ): Promise<VisualizationDetectionResult> {
    const normalizedMessage = message.toLowerCase();
    const normalizedContext = context.toLowerCase();
    const fullText = `${normalizedMessage} ${normalizedContext}`;

    // Check for explicit visualization requests
    const hasVisualizationTrigger = this.VISUALIZATION_TRIGGERS.some(trigger => 
      normalizedMessage.includes(trigger)
    );

    // Detect subject matter
    const detectedSubject = this.detectSubject(fullText);
    
    // Determine if the topic would benefit from visualization
    const topicAnalysis = this.analyzeTopicComplexity(fullText, detectedSubject);
    
    // Calculate confidence score
    let confidence = 0;
    
    if (hasVisualizationTrigger) confidence += 0.4;
    if (topicAnalysis.hasVisualConcepts) confidence += 0.3;
    if (topicAnalysis.isComplex) confidence += 0.2;
    if (this.hasLearningDifficulty(normalizedMessage)) confidence += 0.1;

    // Adjust confidence based on grade level
    confidence = this.adjustConfidenceForGradeLevel(confidence, gradeLevel, topicAnalysis.complexity);

    // Determine visualization type
    const visualizationType = this.determineVisualizationType(fullText, detectedSubject);

    // Check if grade appropriate
    const gradeAppropriate = this.isGradeAppropriate(
      detectedSubject, 
      topicAnalysis.complexity, 
      gradeLevel
    );

    return {
      shouldVisualize: confidence >= 0.5 && gradeAppropriate,
      subject: detectedSubject,
      topic: this.extractTopic(fullText, detectedSubject),
      suggestedVisualization: this.suggestVisualization(detectedSubject, topicAnalysis),
      confidence,
      gradeAppropriate,
      visualizationType,
      estimatedComplexity: this.estimateComplexity(gradeLevel, topicAnalysis.complexity),
      reasoning: this.generateReasoning(hasVisualizationTrigger, topicAnalysis, confidence)
    };
  }

  private detectSubject(text: string): Subject {
    let maxScore = 0;
    let detectedSubject: Subject = 'general';

    Object.entries(this.SUBJECT_KEYWORDS).forEach(([subject, data]) => {
      let score = 0;
      
      // Check keywords
      data.keywords.forEach(keyword => {
        if (text.includes(keyword)) score += 1;
      });
      
      // Check concepts (higher weight)
      data.concepts.forEach(concept => {
        if (text.includes(concept)) score += 2;
      });
      
      // Check visualization triggers
      data.visualizationTriggers.forEach(trigger => {
        if (text.includes(trigger)) score += 1.5;
      });

      if (score > maxScore) {
        maxScore = score;
        detectedSubject = subject as Subject;
      }
    });

    return detectedSubject;
  }

  private analyzeTopicComplexity(text: string, subject: Subject) {
    const subjectData = this.SUBJECT_KEYWORDS[subject];
    
    const hasVisualConcepts = subjectData.visualizationTriggers.some(trigger => 
      text.includes(trigger)
    );
    
    const conceptCount = subjectData.concepts.filter(concept => 
      text.includes(concept)
    ).length;
    
    const isComplex = conceptCount > 1 || text.length > 100;
    
    return {
      hasVisualConcepts,
      isComplex,
      complexity: Math.min(conceptCount + (text.length / 50), 10)
    };
  }

  private hasLearningDifficulty(message: string): boolean {
    const difficultyIndicators = [
      'don\'t understand', 'confused', 'difficult', 'hard to grasp',
      'can\'t visualize', 'struggling with', 'need help', 'unclear'
    ];
    
    return difficultyIndicators.some(indicator => message.includes(indicator));
  }

  private adjustConfidenceForGradeLevel(
    confidence: number, 
    gradeLevel: GradeLevel, 
    complexity: number
  ): number {
    // Visual learning is more beneficial for younger students
    const gradeMultipliers = {
      elementary: 1.2,
      middle: 1.1,
      high: 1.0,
      college: 0.9
    };
    
    const multiplier = gradeMultipliers[gradeLevel] || 1.0;
    
    // Reduce confidence for overly complex topics for lower grades
    if (gradeLevel === 'elementary' && complexity > 7) {
      return confidence * 0.7;
    }
    if (gradeLevel === 'middle' && complexity > 8) {
      return confidence * 0.8;
    }
    
    return Math.min(confidence * multiplier, 1.0);
  }

  private determineVisualizationType(text: string, subject: Subject): VisualizationType {
    if (text.includes('3d') || text.includes('model') || text.includes('structure')) {
      return '3d-model';
    }
    if (text.includes('animate') || text.includes('motion') || text.includes('process')) {
      return 'animation';
    }
    if (text.includes('simulate') || text.includes('experiment')) {
      return 'simulation';
    }
    if (text.includes('graph') || text.includes('plot') || text.includes('chart')) {
      return 'interactive-chart';
    }
    if (text.includes('diagram') || text.includes('flowchart')) {
      return 'diagram';
    }
    
    // Default based on subject
    const subjectDefaults: { [key in Subject]: VisualizationType } = {
      physics: 'animation',
      mathematics: 'interactive-chart',
      chemistry: '3d-model',
      biology: 'animation',
      'computer-science': 'diagram',
      engineering: 'simulation',
      economics: 'interactive-chart',
      general: 'diagram'
    };
    
    return subjectDefaults[subject];
  }

  private isGradeAppropriate(subject: Subject, complexity: number, gradeLevel: GradeLevel): boolean {
    const maxComplexityByGrade = {
      elementary: 4,
      middle: 6,
      high: 8,
      college: 10
    };
    
    return complexity <= maxComplexityByGrade[gradeLevel];
  }

  private estimateComplexity(gradeLevel: GradeLevel, topicComplexity: number): DifficultyLevel {
    const gradeBaseComplexity = {
      elementary: 2,
      middle: 4,
      high: 6,
      college: 8
    };
    
    const adjustedComplexity = topicComplexity + gradeBaseComplexity[gradeLevel];
    
    if (adjustedComplexity <= 4) return 'beginner';
    if (adjustedComplexity <= 7) return 'intermediate';
    return 'advanced';
  }

  private extractTopic(text: string, subject: Subject): string {
    const subjectData = this.SUBJECT_KEYWORDS[subject];
    
    // Find the most relevant concept mentioned
    for (const concept of subjectData.concepts) {
      if (text.includes(concept)) {
        return concept;
      }
    }
    
    // Fall back to first keyword found
    for (const keyword of subjectData.keywords) {
      if (text.includes(keyword)) {
        return keyword;
      }
    }
    
    return 'general concept';
  }

  private suggestVisualization(subject: Subject, analysis: any): string {
    const suggestions = {
      physics: 'Interactive physics simulation showing motion and forces',
      mathematics: 'Dynamic graph with adjustable parameters',
      chemistry: '3D molecular model with interactive bonds',
      biology: 'Animated biological process with step-by-step progression',
      'computer-science': 'Algorithm visualization with step-by-step execution',
      engineering: 'System diagram with interactive components',
      economics: 'Interactive economic model with supply/demand curves',
      general: 'Conceptual diagram with interactive elements'
    };
    
    return suggestions[subject];
  }

  private generateReasoning(
    hasVisualizationTrigger: boolean, 
    topicAnalysis: any, 
    confidence: number
  ): string {
    const reasons = [];
    
    if (hasVisualizationTrigger) {
      reasons.push('User explicitly requested visual explanation');
    }
    if (topicAnalysis.hasVisualConcepts) {
      reasons.push('Topic involves visual or spatial concepts');
    }
    if (topicAnalysis.isComplex) {
      reasons.push('Complex topic would benefit from visual breakdown');
    }
    if (confidence >= 0.7) {
      reasons.push('High confidence that visualization will improve understanding');
    }
    
    return reasons.join('; ');
  }
}

export const visualizationDetectionService = VisualizationDetectionService.getInstance();
