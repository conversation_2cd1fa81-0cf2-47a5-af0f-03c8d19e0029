import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate, useLocation } from "react-router-dom";
import { AuthProvider, useAuth } from "@/contexts/AuthContext";
import { Suspense, lazy } from "react";
import Index from "./pages/Index";
import HomePage from "./pages/HomePage";
import PricingPage from "./pages/PricingPage";
import DocsPage from "./pages/DocsPage";
import ContactPage from "./pages/ContactPage";
import AboutPage from "./pages/AboutPage";
import LoginPage from "./pages/LoginPage";
import SignupPage from "./pages/SignupPage";
import AuthCallbackPage from "./pages/AuthCallbackPage";
import NotFound from "./pages/NotFound";
import OAuthDiagnostics from "./pages/OAuthDiagnostics";
import AuthTestPage from "./pages/AuthTestPage";
import DataVisualizationDemo from "./pages/DataVisualizationDemo";
import FlowchartTest from "./pages/FlowchartTest";

const queryClient = new QueryClient();

// Protected Route Component
const ProtectedRoute = ({ children }: { children: React.ReactNode }) => {
  const { user, loading } = useAuth();
  const location = useLocation();
  
  // Show loading indicator while checking auth status
  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }
  
  // If not authenticated, redirect to login with return url
  if (!user) {
    return <Navigate to={`/login?returnUrl=${encodeURIComponent(location.pathname)}`} replace />;
  }
  
  // Render children if authenticated
  return <>{children}</>;
};

// Public Route Component - redirects to app if already logged in
const PublicRoute = ({ children, redirectIfAuthenticated = true }: { children: React.ReactNode, redirectIfAuthenticated?: boolean }) => {
  const { user, loading } = useAuth();
  
  // Don't redirect during loading or if explicitly specified not to redirect
  if (loading || !redirectIfAuthenticated) {
    return <>{children}</>;
  }
  
  // Redirect to app if already authenticated
  if (user) {
    return <Navigate to="/app" replace />;
  }
  
  // Render children if not authenticated
  return <>{children}</>;
};

const App = () => (
  <QueryClientProvider client={queryClient}>
    <AuthProvider>
      <TooltipProvider>
        <Toaster />
        <Sonner />
        <BrowserRouter>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<HomePage />} />
            <Route path="/pricing" element={<PricingPage />} />
            <Route path="/docs" element={<DocsPage />} />
            <Route path="/contact" element={<ContactPage />} />
            <Route path="/about" element={<AboutPage />} />
            <Route path="/auth/callback" element={<AuthCallbackPage />} />
            
            {/* Auth routes - redirect to app if already logged in */}
            <Route path="/login" element={
              <PublicRoute>
                <LoginPage />
              </PublicRoute>
            } />
            <Route path="/signup" element={
              <PublicRoute>
                <SignupPage />
              </PublicRoute>
            } />
            
            {/* Protected routes - require authentication */}
            <Route path="/app" element={
              <ProtectedRoute>
                <Index />
              </ProtectedRoute>
            } />
            
            {/* OAuth Diagnostics route - publicly accessible */}
            <Route path="/oauth-diagnostics" element={<OAuthDiagnostics />} />

            {/* Auth Test route - publicly accessible */}
            <Route path="/auth-test" element={<AuthTestPage />} />

            {/* Data Visualization Demo - publicly accessible for testing */}
            <Route path="/data-viz-demo" element={<DataVisualizationDemo />} />

            {/* Flowchart Fun Test - publicly accessible for testing */}
            <Route path="/flowchart-test" element={<FlowchartTest />} />

            {/* 404 route */}
            <Route path="*" element={<NotFound />} />
          </Routes>
        </BrowserRouter>
      </TooltipProvider>
    </AuthProvider>
  </QueryClientProvider>
);

export default App;
