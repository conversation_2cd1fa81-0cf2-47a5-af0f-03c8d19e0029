/**
 * <PERSON><PERSON> Settings Component
 * Manages user preferences and tutoring configuration
 */

import React, { useState } from 'react';
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Slider } from "@/components/ui/slider";
import { Separator } from "@/components/ui/separator";
import { 
  Settings, 
  Brain, 
  Zap, 
  BookOpen, 
  Bell,
  ArrowLeft,
  Save,
  RotateCcw
} from "lucide-react";
import { TutorSettings as TutorSettingsType } from '../types';
import { TUTOR_AI_MODELS, EDUCATION_LEVELS, LEARNING_STYLES, DEFAULT_TUTOR_SETTINGS } from '../constants';

interface TutorSettingsProps {
  settings: TutorSettingsType;
  onUpdateSettings: (settings: Partial<TutorSettingsType>) => void;
  onBack: () => void;
}

export function TutorSettings({ settings, onUpdateSettings, onBack }: TutorSettingsProps) {
  const [localSettings, setLocalSettings] = useState<TutorSettingsType>(settings);
  const [hasChanges, setHasChanges] = useState(false);

  const updateLocalSetting = <K extends keyof TutorSettingsType>(
    key: K,
    value: TutorSettingsType[K]
  ) => {
    setLocalSettings(prev => ({ ...prev, [key]: value }));
    setHasChanges(true);
  };

  const updateNotificationSetting = (key: keyof TutorSettingsType['notifications'], value: boolean) => {
    setLocalSettings(prev => ({
      ...prev,
      notifications: { ...prev.notifications, [key]: value }
    }));
    setHasChanges(true);
  };

  const handleSave = () => {
    onUpdateSettings(localSettings);
    setHasChanges(false);
  };

  const handleReset = () => {
    setLocalSettings(DEFAULT_TUTOR_SETTINGS);
    setHasChanges(true);
  };

  const selectedModel = TUTOR_AI_MODELS.find(m => m.id === localSettings.preferredModel);

  return (
    <div className="max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-3">
          <Button variant="ghost" onClick={onBack} className="p-2">
            <ArrowLeft className="w-5 h-5" />
          </Button>
          <div className="flex items-center justify-center w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl">
            <Settings className="w-6 h-6 text-white" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Tutor Settings</h1>
            <p className="text-gray-600">Customize your learning experience</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button variant="outline" onClick={handleReset}>
            <RotateCcw className="w-4 h-4 mr-2" />
            Reset
          </Button>
          <Button 
            onClick={handleSave} 
            disabled={!hasChanges}
            className="bg-blue-500 hover:bg-blue-600"
          >
            <Save className="w-4 h-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* AI Model Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Brain className="w-5 h-5 text-blue-500" />
              <span>AI Model Selection</span>
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Choose your preferred AI model. Google models use Gemini API, others use OpenRouter API.
            </p>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="model">Preferred AI Model</Label>
              <Select
                value={localSettings.preferredModel}
                onValueChange={(value) => updateLocalSetting('preferredModel', value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent className="max-h-80">
                  {/* Google Models (Direct Gemini API) */}
                  <div className="px-2 py-1.5 text-xs font-semibold text-blue-600 bg-blue-50 border-b">
                    🔵 Google Models (Gemini API)
                  </div>
                  {TUTOR_AI_MODELS.filter(model => model.apiProvider === 'gemini').map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex flex-col w-full">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{model.name}</span>
                          <span className="text-xs px-2 py-0.5 bg-blue-100 text-blue-700 rounded-full ml-2">
                            {model.cost}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{model.description}</span>
                        <span className="text-xs text-blue-600">✓ Uses Gemini API Key</span>
                      </div>
                    </SelectItem>
                  ))}

                  {/* OpenRouter Models */}
                  <div className="px-2 py-1.5 text-xs font-semibold text-purple-600 bg-purple-50 border-b border-t mt-1">
                    🟣 OpenRouter Models
                  </div>
                  {TUTOR_AI_MODELS.filter(model => model.apiProvider === 'openrouter').map((model) => (
                    <SelectItem key={model.id} value={model.id}>
                      <div className="flex flex-col w-full">
                        <div className="flex items-center justify-between">
                          <span className="font-medium">{model.name}</span>
                          <span className="text-xs px-2 py-0.5 bg-purple-100 text-purple-700 rounded-full ml-2">
                            {model.cost}
                          </span>
                        </div>
                        <span className="text-sm text-gray-500">{model.description}</span>
                        <span className="text-xs text-purple-600">✓ Uses OpenRouter API Key • {model.provider}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {selectedModel && (
              <div className={`p-4 rounded-lg border-l-4 ${
                selectedModel.apiProvider === 'gemini'
                  ? 'bg-blue-50 border-blue-400'
                  : 'bg-purple-50 border-purple-400'
              }`}>
                <div className="text-sm">
                  <div className="font-medium mb-2 flex items-center">
                    {selectedModel.apiProvider === 'gemini' ? '🔵' : '🟣'} Model Details:
                  </div>
                  <div className="space-y-1 text-gray-700">
                    <div><strong>Provider:</strong> {selectedModel.provider}</div>
                    <div><strong>API Service:</strong> {selectedModel.apiProvider === 'gemini' ? 'Google Gemini API' : 'OpenRouter API'}</div>
                    <div><strong>Cost Level:</strong> {selectedModel.cost}</div>
                    <div><strong>Max Tokens:</strong> {selectedModel.maxTokens.toLocaleString()}</div>
                    <div><strong>Streaming:</strong> {selectedModel.supportsStreaming ? '✅ Yes' : '❌ No'}</div>
                    <div><strong>Strengths:</strong> {selectedModel.strengths.join(', ')}</div>
                    <div><strong>Best For:</strong> {selectedModel.bestFor.join(', ')}</div>
                    <div><strong>Education Levels:</strong> {selectedModel.educationLevels.map(level => level.replace('-', ' ')).join(', ')}</div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Learning Preferences */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Zap className="w-5 h-5 text-green-500" />
              <span>Learning Preferences</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="education-level">Default Education Level</Label>
              <Select 
                value={localSettings.educationLevel} 
                onValueChange={(value) => updateLocalSetting('educationLevel', value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {EDUCATION_LEVELS.map((level) => (
                    <SelectItem key={level.id} value={level.id}>
                      <div className="flex items-center space-x-2">
                        <level.icon className="w-4 h-4" />
                        <span>{level.name}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="learning-style">Learning Style</Label>
              <Select 
                value={localSettings.learningStyle} 
                onValueChange={(value) => updateLocalSetting('learningStyle', value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {LEARNING_STYLES.map((style) => (
                    <SelectItem key={style.id} value={style.id}>
                      <div className="flex flex-col">
                        <span className="font-medium">{style.name}</span>
                        <span className="text-sm text-gray-500">{style.description}</span>
                      </div>
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="pace">Learning Pace</Label>
              <Select 
                value={localSettings.pace} 
                onValueChange={(value) => updateLocalSetting('pace', value as any)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="slow">Slow - Take time to understand</SelectItem>
                  <SelectItem value="normal">Normal - Balanced pace</SelectItem>
                  <SelectItem value="fast">Fast - Quick explanations</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Content Settings */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BookOpen className="w-5 h-5 text-purple-500" />
              <span>Content Settings</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="include-examples">Include Examples</Label>
                <p className="text-sm text-gray-500">Show examples and analogies in explanations</p>
              </div>
              <Switch
                id="include-examples"
                checked={localSettings.includeExamples}
                onCheckedChange={(checked) => updateLocalSetting('includeExamples', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="include-quizzes">Include Quizzes</Label>
                <p className="text-sm text-gray-500">Add quiz questions to test understanding</p>
              </div>
              <Switch
                id="include-quizzes"
                checked={localSettings.includeQuizzes}
                onCheckedChange={(checked) => updateLocalSetting('includeQuizzes', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="sources-enabled">Enable Sources</Label>
                <p className="text-sm text-gray-500">Show educational sources and references</p>
              </div>
              <Switch
                id="sources-enabled"
                checked={localSettings.sourcesEnabled}
                onCheckedChange={(checked) => updateLocalSetting('sourcesEnabled', checked)}
              />
            </div>

            {localSettings.sourcesEnabled && (
              <div>
                <Label htmlFor="max-sources">Maximum Sources: {localSettings.maxSources}</Label>
                <Slider
                  id="max-sources"
                  min={3}
                  max={15}
                  step={1}
                  value={[localSettings.maxSources]}
                  onValueChange={([value]) => updateLocalSetting('maxSources', value)}
                  className="mt-2"
                />
              </div>
            )}
          </CardContent>
        </Card>

        {/* Notifications */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Bell className="w-5 h-5 text-orange-500" />
              <span>Notifications</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="session-reminders">Session Reminders</Label>
                <p className="text-sm text-gray-500">Get reminded to continue learning</p>
              </div>
              <Switch
                id="session-reminders"
                checked={localSettings.notifications.sessionReminders}
                onCheckedChange={(checked) => updateNotificationSetting('sessionReminders', checked)}
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="progress-updates">Progress Updates</Label>
                <p className="text-sm text-gray-500">Receive updates on learning progress</p>
              </div>
              <Switch
                id="progress-updates"
                checked={localSettings.notifications.progressUpdates}
                onCheckedChange={(checked) => updateNotificationSetting('progressUpdates', checked)}
              />
            </div>
          </CardContent>
        </Card>
      </div>

      {hasChanges && (
        <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-blue-800 text-sm">
            You have unsaved changes. Click "Save Changes" to apply your new settings.
          </p>
        </div>
      )}
    </div>
  );
}
