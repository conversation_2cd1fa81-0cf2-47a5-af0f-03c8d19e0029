/**
 * AI Tutor Store
 * Zustand store for managing tutoring session state
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  TutorStoreState,
  TutorSession,
  TutorMessage,
  EducationLevel,
  TutorSettings,
  VisualizationState,
  AnimationControls,
  EducationalVisualization,
  VisualizationHistoryItem,
  VisualizationDetectionResult,
  SubjectVisualization,
  GradeLevel,
  DifficultyLevel
} from '../types';
import { DEFAULT_TUTOR_SETTINGS } from '../constants';
import tutorAIService from '../services/tutor-ai.service';
import tutorSourcesService from '../services/tutor-sources.service';
import { visualizationDetectionService } from '../services/visualization-detection.service';
import { toast } from 'sonner';

export const useTutorStore = create<TutorStoreState>()(
  persist(
    (set, get) => ({
      // Current session state
      currentSession: null,
      isSessionActive: false,

      // UI state
      currentView: 'hero',
      isLoading: false,
      isStreaming: false,
      error: null,

      // Settings
      settings: DEFAULT_TUTOR_SETTINGS,

      // History
      sessions: [],

      // Sources
      isLoadingSources: false,

      // Visualization state
      visualizationState: {
        currentVisualization: null,
        animationControls: {
          isPlaying: false,
          speed: 1.0,
          currentStep: 0,
          totalSteps: 0,
          canReset: false,
          canStepThrough: false,
          loop: false,
          autoPlay: true
        },
        gradeLevel: 'high',
        preferredComplexity: 'intermediate',
        visualizationHistory: [],
        isVisualizationEnabled: true,
        autoDetectVisualizations: true,
        preferredSubjects: [],
        visualizationPreferences: {
          autoPlay: true,
          showControls: true,
          showExplanations: true,
          enableInteractivity: true,
          preferredSpeed: 1.0
        }
      },
      availableVisualizations: [],
      isGeneratingVisualization: false,

      // Actions
      startSession: async (topic: string, educationLevel: EducationLevel) => {
        set({ isLoading: true, error: null });

        try {
          // Create new session
          const sessionId = `session-${Date.now()}`;
          const newSession: TutorSession = {
            id: sessionId,
            title: `Learning: ${topic}`,
            topic,
            educationLevel,
            messages: [],
            sources: [],
            createdAt: new Date(),
            updatedAt: new Date(),
            isActive: true,
            metadata: {
              totalMessages: 0,
              duration: 0,
              concepts: [],
              difficulty: 'beginner'
            }
          };

          // Load sources for the topic
          set({ isLoadingSources: true });
          try {
            const sourcesResult = await tutorSourcesService.getTopicSources(
              topic,
              educationLevel,
              get().settings.maxSources
            );
            newSession.sources = sourcesResult;
          } catch (error) {
            console.warn('Failed to load sources:', error);
            // Continue without sources
          }
          set({ isLoadingSources: false });

          // Generate initial welcome message
          const welcomeMessage = `Hello! I'm excited to help you learn about ${topic}. I've tailored my explanations for the ${educationLevel.replace('-', ' ')} level. What would you like to know first?`;
          
          const initialMessage: TutorMessage = {
            id: `msg-${Date.now()}`,
            role: 'assistant',
            content: welcomeMessage,
            timestamp: new Date()
          };

          newSession.messages = [initialMessage];
          newSession.metadata!.totalMessages = 1;

          // Update store
          set({
            currentSession: newSession,
            isSessionActive: true,
            currentView: 'chat',
            isLoading: false,
            sessions: [newSession, ...get().sessions]
          });

          toast.success(`Started learning session: ${topic}`);

        } catch (error) {
          console.error('Failed to start session:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to start session',
            isLoading: false 
          });
          toast.error('Failed to start tutoring session');
        }
      },

      sendMessage: async (message: string) => {
        const { currentSession, settings } = get();
        if (!currentSession) return;

        set({ isStreaming: true, error: null });

        try {
          // Add user message
          const userMessage: TutorMessage = {
            id: `msg-${Date.now()}`,
            role: 'user',
            content: message,
            timestamp: new Date()
          };

          const updatedSession = {
            ...currentSession,
            messages: [...currentSession.messages, userMessage],
            updatedAt: new Date()
          };

          set({ currentSession: updatedSession });

          // Check if this is a visualization request first (lightweight detection)
          const isVisualizationRequest = get().isVisualizationRequest(message);

          let assistantMessage: TutorMessage;

          if (isVisualizationRequest) {
            // Generate visualization directly without full text response
            console.log('🎨 Detected visualization request, generating directly...');

            assistantMessage = {
              id: `msg-${Date.now() + 1}`,
              role: 'assistant',
              content: `I'll create a visualization for "${currentSession.topic}" to help you understand this concept better.`,
              timestamp: new Date(),
              metadata: {
                tokens: 0,
                model: settings.preferredModel,
                confidence: 0.9,
                isVisualizationResponse: true
              }
            };

          } else {
            // Prepare conversation history for AI
            const conversationHistory = updatedSession.messages
              .slice(-10) // Last 10 messages for context
              .map(msg => ({
                role: msg.role,
                content: msg.content
              }));

            // Generate regular AI response
            const aiResponse = await tutorAIService.generateTutoringResponse(
              currentSession.topic,
              message,
              {
                model: settings.preferredModel,
                educationLevel: currentSession.educationLevel,
                includeExamples: settings.includeExamples,
                learningStyle: settings.learningStyle,
                conversationHistory
              }
            );

            // Add AI response message
            assistantMessage = {
              id: `msg-${Date.now() + 1}`,
              role: 'assistant',
              content: aiResponse.content,
              timestamp: new Date(),
              metadata: {
                tokens: aiResponse.tokens,
                model: aiResponse.model,
                confidence: aiResponse.confidence
              }
            };
          }

          const finalSession = {
            ...updatedSession,
            messages: [...updatedSession.messages, assistantMessage],
            updatedAt: new Date(),
            metadata: {
              ...updatedSession.metadata!,
              totalMessages: updatedSession.messages.length + 1
            }
          };

          // Update store
          set({
            currentSession: finalSession,
            isStreaming: false,
            sessions: [finalSession, ...get().sessions.filter(s => s.id !== finalSession.id)]
          });

        } catch (error) {
          console.error('Failed to send message:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to send message',
            isStreaming: false 
          });
          toast.error('Failed to send message');
        }
      },

      endSession: () => {
        const { currentSession } = get();
        if (!currentSession) return;

        const endedSession = {
          ...currentSession,
          isActive: false,
          updatedAt: new Date()
        };

        set({
          currentSession: null,
          isSessionActive: false,
          currentView: 'hero',
          sessions: [endedSession, ...get().sessions.filter(s => s.id !== endedSession.id)]
        });

        toast.success('Tutoring session ended');
      },

      loadSession: async (sessionId: string) => {
        set({ isLoading: true, error: null });

        try {
          const session = get().sessions.find(s => s.id === sessionId);
          if (!session) {
            throw new Error('Session not found');
          }

          const reactivatedSession = {
            ...session,
            isActive: true,
            updatedAt: new Date()
          };

          set({
            currentSession: reactivatedSession,
            isSessionActive: true,
            currentView: 'chat',
            isLoading: false
          });

          toast.success(`Resumed session: ${session.title}`);

        } catch (error) {
          console.error('Failed to load session:', error);
          set({ 
            error: error instanceof Error ? error.message : 'Failed to load session',
            isLoading: false 
          });
          toast.error('Failed to load session');
        }
      },

      deleteSession: async (sessionId: string) => {
        try {
          const sessions = get().sessions.filter(s => s.id !== sessionId);
          set({ sessions });

          // If deleting current session, reset to hero view
          if (get().currentSession?.id === sessionId) {
            set({
              currentSession: null,
              isSessionActive: false,
              currentView: 'hero'
            });
          }

          toast.success('Session deleted');

        } catch (error) {
          console.error('Failed to delete session:', error);
          toast.error('Failed to delete session');
        }
      },

      updateSettings: (newSettings: Partial<TutorSettings>) => {
        const currentSettings = get().settings;
        const updatedSettings = { ...currentSettings, ...newSettings };
        
        set({ settings: updatedSettings });
        toast.success('Settings updated');
      },

      setCurrentView: (view: 'hero' | 'chat' | 'history' | 'settings') => {
        set({ currentView: view });
      },

      clearError: () => {
        set({ error: null });
      },

      // Visualization actions
      detectVisualization: async (message: string, context: string): Promise<VisualizationDetectionResult> => {
        const { visualizationState } = get();

        try {
          const detection = await visualizationDetectionService.detectVisualizationNeed(
            message,
            context,
            visualizationState.gradeLevel,
            []
          );

          return detection;
        } catch (error) {
          console.error('Visualization detection failed:', error);
          throw error;
        }
      },

      generateVisualization: async (detection: VisualizationDetectionResult): Promise<EducationalVisualization> => {
        set({ isGeneratingVisualization: true });

        try {
          // Create a basic visualization object
          // In a real implementation, this would generate the actual visualization
          const visualization: EducationalVisualization = {
            id: `viz-${Date.now()}`,
            subject: detection.subject,
            topic: detection.topic,
            title: `Interactive ${detection.subject} Visualization`,
            description: detection.suggestedVisualization,
            gradeLevel: detection.estimatedComplexity === 'beginner' ? 'elementary' :
                       detection.estimatedComplexity === 'intermediate' ? 'middle' : 'high',
            difficulty: detection.estimatedComplexity,
            visualizationType: detection.visualizationType,
            parameters: {},
            learningObjectives: [`Understand ${detection.topic}`, `Visualize ${detection.subject} concepts`],
            keyPoints: [`Key concept: ${detection.topic}`, `Subject: ${detection.subject}`],
            interactiveElements: ['Play/Pause controls', 'Speed adjustment', 'Parameter sliders'],
            estimatedDuration: 5,
            relatedConcepts: [],
            createdAt: new Date(),
            updatedAt: new Date()
          };

          set({
            isGeneratingVisualization: false,
            visualizationState: {
              ...get().visualizationState,
              currentVisualization: visualization
            }
          });

          return visualization;
        } catch (error) {
          set({ isGeneratingVisualization: false });
          console.error('Visualization generation failed:', error);
          throw error;
        }
      },

      updateAnimationControls: (controls: Partial<AnimationControls>) => {
        const { visualizationState } = get();
        set({
          visualizationState: {
            ...visualizationState,
            animationControls: {
              ...visualizationState.animationControls,
              ...controls
            }
          }
        });
      },

      updateVisualizationPreferences: (preferences: Partial<VisualizationState['visualizationPreferences']>) => {
        const { visualizationState } = get();
        set({
          visualizationState: {
            ...visualizationState,
            visualizationPreferences: {
              ...visualizationState.visualizationPreferences,
              ...preferences
            }
          }
        });
      },

      addVisualizationToHistory: (item: VisualizationHistoryItem) => {
        const { visualizationState } = get();
        const updatedHistory = [item, ...visualizationState.visualizationHistory].slice(0, 100); // Keep last 100

        set({
          visualizationState: {
            ...visualizationState,
            visualizationHistory: updatedHistory
          }
        });
      },

      setCurrentVisualization: (visualization: EducationalVisualization | null) => {
        const { visualizationState } = get();
        set({
          visualizationState: {
            ...visualizationState,
            currentVisualization: visualization
          }
        });
      },

      // Lightweight visualization detection (no AI calls)
      isVisualizationRequest: (message: string): boolean => {
        const normalizedMessage = message.toLowerCase();

        // Direct visualization keywords
        const visualizationKeywords = [
          'visualize', 'visualization', 'show me', 'demonstrate', 'illustrate',
          'animate', 'animation', 'simulate', 'simulation', 'draw', 'display',
          'chart', 'graph', 'plot', 'diagram', 'model', '3d', 'interactive',
          'visual', 'picture', 'image', 'render', 'create a', 'generate a',
          'build a', 'make a'
        ];

        // Topic-specific visualization triggers
        const topicTriggers = [
          'how does', 'what happens when', 'show the process', 'step by step',
          'molecular structure', 'chemical reaction', 'physics simulation',
          'mathematical function', 'data flow', 'algorithm visualization',
          'biological process', 'cell division', 'ecosystem', 'solar system',
          'global warming', 'climate change', 'greenhouse effect',
          'water cycle', 'carbon cycle', 'nitrogen cycle', 'rock cycle',
          'how it start', 'how it works', 'the process of', 'cycle that',
          'from earth then sky', 'evaporation', 'condensation', 'precipitation'
        ];

        // Check for explicit visualization requests
        const hasVisualizationKeyword = visualizationKeywords.some(keyword =>
          normalizedMessage.includes(keyword)
        );

        // Check for topic-specific triggers
        const hasTopicTrigger = topicTriggers.some(trigger =>
          normalizedMessage.includes(trigger)
        );

        // Check for question patterns that benefit from visualization
        const questionPatterns = [
          /how does .* work/,
          /what is .* like/,
          /show me .*/,
          /can you .* visualize/,
          /i want to see/,
          /demonstrate .*/,
          /explain .* with/,
          /explain .* that how/,
          /how .* start .* and .* back/,
          /from .* then .* and .* back/,
          /the .* cycle/,
          /process of .*/,
          /how .* happens/
        ];

        const hasQuestionPattern = questionPatterns.some(pattern =>
          pattern.test(normalizedMessage)
        );

        // Additional check: if message is asking about a scientific/educational process
        const educationalTopics = [
          'cycle', 'process', 'system', 'structure', 'function', 'mechanism',
          'reaction', 'interaction', 'formation', 'development', 'evolution',
          'transformation', 'movement', 'flow', 'circulation', 'rotation'
        ];

        const hasEducationalTopic = educationalTopics.some(topic =>
          normalizedMessage.includes(topic)
        );

        // If it's an educational topic and asking for explanation, prefer visualization
        const isExplanationRequest = normalizedMessage.includes('explain') ||
                                   normalizedMessage.includes('tell me') ||
                                   normalizedMessage.includes('how') ||
                                   normalizedMessage.includes('what');

        const shouldVisualize = hasVisualizationKeyword ||
                               hasTopicTrigger ||
                               hasQuestionPattern ||
                               (hasEducationalTopic && isExplanationRequest);

        console.log('🔍 Visualization detection:', {
          message: normalizedMessage,
          hasVisualizationKeyword,
          hasTopicTrigger,
          hasQuestionPattern,
          hasEducationalTopic,
          isExplanationRequest,
          shouldVisualize
        });

        return shouldVisualize;
      }
    }),
    {
      name: 'ai-tutor-store',
      partialize: (state) => ({
        settings: state.settings,
        sessions: state.sessions.slice(0, 50), // Keep only last 50 sessions
        visualizationState: {
          ...state.visualizationState,
          currentVisualization: null, // Don't persist current visualization
          visualizationHistory: state.visualizationState.visualizationHistory.slice(0, 50) // Keep last 50
        }
      })
    }
  )
);
