/**
 * Dynamic Visualization Renderer
 * Safely renders AI-generated visualization code
 */

import React, { useState, useEffect, useMemo, Suspense } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Play,
  Pause,
  RotateCcw,
  Code,
  Eye,
  EyeOff,
  Maximize2,
  Minimize2,
  Download,
  Share2,
  AlertTriangle,
  Lightbulb,
  Search,
  RefreshCw,
  X
} from "lucide-react";
import { aiVisualizationGenerator } from '../../services/ai-visualization-generator.service';
import { Subject, EducationLevel, VisualizationType } from '../../types';
import { SafeCodeExecutor } from './SafeCodeExecutor';

interface DynamicVisualizationProps {
  topic: string;
  subject: Subject;
  educationLevel: EducationLevel;
  visualizationType: VisualizationType;
  userQuestion: string;
  context?: string;
  model?: string;
  className?: string;
  onVisualizationGenerated?: (visualization: any) => void;
}

interface GeneratedVisualization {
  code: string;
  type: 'react' | 'html' | 'd3' | 'plotly' | 'three';
  title: string;
  description: string;
  interactiveElements: string[];
  educationalNotes: string[];
  dependencies: string[];
  complexity: 'simple' | 'intermediate' | 'advanced';
}

export const DynamicVisualizationRenderer: React.FC<DynamicVisualizationProps> = ({
  topic,
  subject,
  educationLevel,
  visualizationType,
  userQuestion,
  context,
  model,
  className = '',
  onVisualizationGenerated
}) => {
  const [visualization, setVisualization] = useState<GeneratedVisualization | null>(null);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showCode, setShowCode] = useState(false);
  const [showCodeReview, setShowCodeReview] = useState(false);
  const [codeReviewFeedback, setCodeReviewFeedback] = useState<string>('');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);

  // New state for 2-step process
  const [generatedCode, setGeneratedCode] = useState<string | null>(null);
  const [isCodeExecuted, setIsCodeExecuted] = useState(false);
  const [showCodePreview, setShowCodePreview] = useState(true);
  const [debugResponse, setDebugResponse] = useState<string>('');

  // Generate visualization code on mount (but don't execute)
  useEffect(() => {
    generateVisualizationCode();
  }, [topic, subject, educationLevel, visualizationType, userQuestion]);

  // Step 1: Generate code only (don't execute)
  const generateVisualizationCode = async () => {
    setIsGenerating(true);
    setError(null);
    setGeneratedCode(null);
    setIsCodeExecuted(false);

    try {
      const request = {
        topic,
        subject,
        educationLevel,
        visualizationType,
        userQuestion,
        context,
        model
      };

      console.log('🎨 Generating visualization code for:', topic);

      const result = await aiVisualizationGenerator.generateVisualization(request);

      // Store the generated code and auto-execute it
      setGeneratedCode(result.code);
      setVisualization(result); // Store metadata

      console.log('✅ Code generated successfully:', {
        title: result.title,
        type: result.type,
        complexity: result.complexity,
        codeLength: result.code.length
      });

      // Auto-execute the code immediately after generation
      setTimeout(() => {
        executeVisualizationCode();
      }, 100);

    } catch (err) {
      console.error('❌ Code generation failed:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to generate code';

      // Provide specific guidance based on error type
      let userFriendlyError = '';
      if (errorMessage.includes('timeout')) {
        userFriendlyError = `⏱️ AI Request Timed Out\n\nThe AI is taking too long to generate code. Try:\n• Using a simpler topic (e.g., "basic wave motion" instead of "quantum wave interference")\n• Switching to a faster model (Gemini Flash)\n• Asking for a "simple animation" instead of complex visualization`;
      } else if (errorMessage.includes('503') || errorMessage.includes('overloaded')) {
        userFriendlyError = `🔄 AI Service Busy\n\nThe AI service is currently overloaded. Try:\n• Waiting a few seconds and trying again\n• Using a different model\n• Simplifying your request`;
      } else if (errorMessage.includes('API key') || errorMessage.includes('authentication')) {
        userFriendlyError = `🔑 API Configuration Issue\n\nThere's a problem with the API setup. Check:\n• API keys are correctly configured\n• Internet connection is stable\n• API quotas haven't been exceeded`;
      } else {
        userFriendlyError = `❌ Generation Failed: ${errorMessage}\n\nTry:\n• Simplifying your topic\n• Using different keywords\n• Asking for a "basic animation"`;
      }

      setError(userFriendlyError);
    } finally {
      setIsGenerating(false);
    }
  };

  // Step 2: Execute the generated code
  const executeVisualizationCode = () => {
    if (!generatedCode) {
      setError('No code to execute. Please generate code first.');
      return;
    }

    console.log('🚀 Executing visualization code...');
    setIsCodeExecuted(true);
    setShowCodePreview(false);

    // The SafeCodeExecutor will handle the actual execution
    onVisualizationGenerated?.(visualization);
  };

  // Removed fallback test function - we only show real AI-generated content or errors

  // Create safe component from generated code using SafeCodeExecutor (only when executed)
  const VisualizationComponent = useMemo(() => {
    if (!isCodeExecuted || !generatedCode || !visualization) return null;

    console.log('🚀 Creating visualization component from code:', {
      title: visualization.title,
      type: visualization.type,
      codeLength: generatedCode.length
    });

    return (
      <SafeCodeExecutor
        code={generatedCode}
        type={visualization.type}
        title={visualization.title}
        description={visualization.description}
        dependencies={visualization.dependencies}
        onError={(error) => {
          console.error('❌ Visualization execution error:', error);
          setError(`Code execution failed: ${error}\n\n🔄 Try Again\n💡 Suggestions: Try asking for the topic in simpler terms, or request a basic animation instead of complex visualizations.`);
        }}
        onSuccess={() => {
          console.log('✅ Visualization executed successfully');
        }}
      />
    );
  }, [isCodeExecuted, generatedCode, visualization]);

  const handleRegenerate = () => {
    setVisualization(null);
    setGeneratedCode(null);
    setIsCodeExecuted(false);
    setShowCodePreview(true);
    setError(null);
    generateVisualizationCode();
  };

  const handleRegenerateWithFastModel = () => {
    console.log('⚡ Regenerating with fast model...');

    const request = {
      topic,
      subject,
      educationLevel,
      visualizationType,
      userQuestion: `Create a simple ${topic} animation`,
      context: `Simple interactive visualization for ${topic}`,
      model: 'google/gemini-2.5-flash' // Force fastest model
    };

    setIsGenerating(true);
    setError(null);
    setVisualization(null);
    setGeneratedCode(null);
    setIsCodeExecuted(false);

    aiVisualizationGenerator.generateVisualization(request)
      .then(result => {
        setGeneratedCode(result.code);
        setVisualization(result);
        console.log('✅ Fast model generation successful');

        // Auto-execute the code
        setTimeout(() => {
          executeVisualizationCode();
        }, 100);
      })
      .catch(err => {
        console.error('❌ Fast model generation failed:', err);
        setError(`Even the fast model failed: ${err.message}\n\nTry:\n• A much simpler topic\n• Basic keywords only\n• Check your internet connection`);
      })
      .finally(() => {
        setIsGenerating(false);
      });
  };

  const handleTestSimpleAnimation = () => {
    console.log('🧪 Testing with minimal animation...');

    const request = {
      topic: 'bouncing ball',
      subject: 'physics',
      educationLevel: 'middle' as const,
      visualizationType: 'animated' as const,
      userQuestion: 'simple bouncing ball animation',
      context: 'basic physics animation test',
      model: 'google/gemini-2.5-flash'
    };

    setIsGenerating(true);
    setError(null);
    setVisualization(null);
    setGeneratedCode(null);
    setIsCodeExecuted(false);

    aiVisualizationGenerator.generateVisualization(request)
      .then(result => {
        setGeneratedCode(result.code);
        setVisualization(result);
        console.log('✅ Test animation generated successfully');

        // Auto-execute the code
        setTimeout(() => {
          executeVisualizationCode();
        }, 100);
      })
      .catch(err => {
        console.error('❌ Test animation failed:', err);
        setError(`Test failed: ${err.message}\n\nThis suggests there may be an API configuration issue.`);
      })
      .finally(() => {
        setIsGenerating(false);
      });
  };

  const handleDebugTest = async () => {
    console.log('🔍 Running debug test...');

    // Test with direct API call to see raw response
    try {
      setIsGenerating(true);
      setError(null);
      setDebugResponse('');

      const { geminiTutorService } = await import('../../services/gemini-tutor.service');

      const prompt = `Generate ONLY React component code for water cycle visualization. NO explanations, JUST CODE:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

export default function WaterCycleVisualization() {
  // Your code here
}
\`\`\``;

      const response = await geminiTutorService.generateTutoringResponse('visualization', prompt, {
        model: 'google/gemini-2.5-flash',
        temperature: 0.1,
        maxTokens: 2048,
        educationLevel: 'middle' as const,
        includeExamples: false,
        learningStyle: 'visual' as const,
        conversationHistory: []
      });

      console.log('🔍 Raw AI Response:', response);
      setDebugResponse(JSON.stringify(response, null, 2));

    } catch (err) {
      console.error('🔍 Debug test failed:', err);
      setDebugResponse(`Debug test failed: ${err}`);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleTestDetection = () => {
    console.log('🧪 Testing visualization detection...');

    const testMessages = [
      'please explain me the water cycle that how it start from earth then sky and come back',
      'show me how photosynthesis works',
      'visualize the solar system',
      'what is gravity?',
      'how does the heart pump blood?',
      'explain the carbon cycle'
    ];

    // Import the detection function
    import('../../stores/tutor.store').then(({ useTutorStore }) => {
      const store = useTutorStore.getState();

      const results = testMessages.map(message => ({
        message,
        isVisualization: store.isVisualizationRequest(message)
      }));

      console.table(results);
      setDebugResponse(`Detection Test Results:\n\n${results.map(r =>
        `"${r.message}" → ${r.isVisualization ? '✅ VISUALIZATION' : '❌ TEXT'}`
      ).join('\n\n')}`);
    });
  };

  const handleTogglePlay = () => {
    setIsPlaying(!isPlaying);
  };

  const handleToggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const handleCodeReview = async () => {
    if (!visualization) return;

    setShowCodeReview(true);
    setCodeReviewFeedback('Analyzing code...');

    try {
      // Simple code review - check for common issues
      const code = visualization.code;
      const issues = [];

      // Check for template literal issues
      if (code.includes('${') && !code.includes('\\${')) {
        issues.push('⚠️ Template literals may need escaping in JSX strings');
      }

      // Check for missing semicolons
      const lines = code.split('\n');
      const missingSemicolons = lines.filter(line =>
        line.trim().length > 0 &&
        !line.trim().endsWith(';') &&
        !line.trim().endsWith('{') &&
        !line.trim().endsWith('}') &&
        !line.trim().endsWith(',') &&
        !line.trim().startsWith('//') &&
        !line.trim().startsWith('*') &&
        line.includes('=') &&
        !line.includes('return')
      );

      if (missingSemicolons.length > 0) {
        issues.push(`⚠️ Possible missing semicolons on ${missingSemicolons.length} lines`);
      }

      // Check for React best practices
      if (!code.includes('useState') && !code.includes('useEffect')) {
        issues.push('💡 Consider adding interactivity with React hooks');
      }

      if (!code.includes('className')) {
        issues.push('💡 Consider adding Tailwind CSS classes for better styling');
      }

      // Generate feedback
      if (issues.length === 0) {
        setCodeReviewFeedback('✅ Code looks good! No major issues detected.');
      } else {
        setCodeReviewFeedback(`Code Review Results:\n\n${issues.join('\n\n')}\n\nYou can ask the AI to fix these issues by regenerating the visualization.`);
      }
    } catch (error) {
      setCodeReviewFeedback('❌ Error during code review: ' + (error instanceof Error ? error.message : 'Unknown error'));
    }
  };

  const handleFixCode = async () => {
    if (!visualization) return;

    // Regenerate with specific instructions to fix issues
    setIsGenerating(true);
    setError(null);
    setShowCodeReview(false);

    try {
      const fixRequest = {
        ...{
          topic,
          subject,
          educationLevel,
          visualizationType,
          userQuestion: userQuestion + ' (Please fix any syntax errors and ensure proper template literal escaping)',
          context: context + ' - Fix syntax issues, escape template literals properly, add semicolons where needed',
          model
        }
      };

      console.log('Regenerating with fix request:', fixRequest);

      const result = await aiVisualizationGenerator.generateVisualization(fixRequest);
      setVisualization(result);
      onVisualizationGenerated?.(result);

      console.log('Fixed visualization generated successfully');
    } catch (err) {
      console.error('Failed to fix visualization:', err);
      setError(err instanceof Error ? err.message : 'Failed to fix visualization');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleDownload = () => {
    if (!visualization) return;
    
    const blob = new Blob([visualization.code], { type: 'text/javascript' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${topic.replace(/\s+/g, '_')}_visualization.js`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isGenerating) {
    return (
      <Card className={`border-0 shadow-lg ${className}`}>
        <CardContent className="p-6">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <motion.div
                className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full mx-auto mb-4"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <h3 className="text-lg font-semibold text-gray-800 mb-2">
                🎨 Creating Visualization
              </h3>
              <p className="text-sm text-gray-600">
                Building interactive {visualizationType} for "{topic}"...
              </p>
              <p className="text-xs text-blue-600 mt-2">
                ⚡ Generating React components and animations
              </p>
              <div className="mt-4 flex flex-wrap gap-2 justify-center">
                <Badge variant="outline">{subject}</Badge>
                <Badge variant="outline">{educationLevel.replace('-', ' ')}</Badge>
                <Badge variant="outline">{visualizationType}</Badge>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={`border-0 shadow-lg ${className}`}>
        <CardContent className="p-6">
          <Alert className="border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertDescription className="text-red-800">
              <div className="space-y-3">
                <div>
                  <strong>Unable to generate visualization:</strong>
                  <pre className="mt-2 text-sm bg-red-100 p-2 rounded whitespace-pre-wrap">{error}</pre>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerate}
                    className="bg-white hover:bg-red-50"
                  >
                    🔄 Try Again
                  </Button>

                  {error?.includes('timeout') && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleRegenerateWithFastModel()}
                      className="bg-white hover:bg-green-50 text-green-700"
                    >
                      ⚡ Try Fast Model
                    </Button>
                  )}

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestSimpleAnimation()}
                    className="bg-white hover:bg-blue-50 text-blue-700"
                  >
                    🧪 Test Simple
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDebugTest()}
                    className="bg-white hover:bg-purple-50 text-purple-700"
                  >
                    🔍 Debug API
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestDetection()}
                    className="bg-white hover:bg-yellow-50 text-yellow-700"
                  >
                    🧪 Test Detection
                  </Button>

                  {generatedCode && !isCodeExecuted && (
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={executeVisualizationCode}
                      className="bg-white hover:bg-blue-50 text-blue-700"
                    >
                      🚀 Run Code
                    </Button>
                  )}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => window.location.reload()}
                    className="bg-white hover:bg-red-50"
                  >
                    🔄 Refresh Page
                  </Button>
                </div>
                <p className="text-sm text-red-700">
                  💡 <strong>Suggestions:</strong> Try asking for the topic in simpler terms, or request a basic animation instead of complex visualizations.
                </p>
              </div>
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  if (!visualization && !generatedCode) {
    return null;
  }

  // Show code preview first (Step 1)
  if (generatedCode && showCodePreview && !isCodeExecuted) {
    return (
      <Card className={`border-0 shadow-lg ${className}`}>
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="text-lg font-semibold text-gray-800">
                🎨 Generated Code for "{topic}"
              </h3>
              <p className="text-sm text-gray-600 mt-1">
                Review the generated code below, then click "Run Code" to execute it.
              </p>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerate}
                className="bg-white hover:bg-gray-50"
              >
                🔄 Regenerate
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={executeVisualizationCode}
                className="bg-green-600 hover:bg-green-700 text-white"
              >
                🚀 Run Code
              </Button>
            </div>
          </div>
        </CardHeader>

        <CardContent className="p-6">
          <div className="bg-gray-900 rounded-lg p-4 overflow-auto max-h-96">
            <pre className="text-green-400 text-sm font-mono whitespace-pre-wrap">
              {generatedCode}
            </pre>
          </div>

          <div className="mt-4 p-3 bg-blue-50 rounded-lg">
            <p className="text-sm text-blue-800">
              💡 <strong>Next Step:</strong> Click "Run Code" to execute this visualization and see it in action!
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`${isFullscreen ? 'fixed inset-0 z-50 bg-white' : ''} ${className}`}>
      <Card className="border-0 shadow-lg h-full">
        {/* Header */}
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <Eye className="w-4 h-4 text-white" />
              </div>
              <div>
                <CardTitle className="text-lg">{visualization.title}</CardTitle>
                <p className="text-sm text-gray-600">{visualization.description}</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Badge variant="secondary">{visualization.complexity}</Badge>
              <Badge variant="outline">{visualization.type}</Badge>
            </div>
          </div>
        </CardHeader>

        {/* Controls */}
        <div className="px-6 pb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleTogglePlay}
              >
                {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleRegenerate}
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowCode(!showCode)}
              >
                {showCode ? <EyeOff className="w-4 h-4" /> : <Code className="w-4 h-4" />}
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleCodeReview}
                disabled={!visualization}
              >
                <Search className="w-4 h-4" />
              </Button>

              <Button
                variant="outline"
                size="sm"
                onClick={handleFixCode}
                disabled={!visualization || isGenerating}
              >
                <RefreshCw className={`w-4 h-4 ${isGenerating ? 'animate-spin' : ''}`} />
              </Button>
            </div>

            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={handleDownload}
              >
                <Download className="w-4 h-4" />
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleToggleFullscreen}
              >
                {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
              </Button>
            </div>
          </div>
        </div>

        {/* Code Review Panel */}
        {showCodeReview && (
          <CardContent className="p-6 pt-0 border-t">
            <div className="bg-gray-50 rounded-lg p-4">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold text-gray-800">Code Review</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowCodeReview(false)}
                >
                  <X className="w-4 h-4" />
                </Button>
              </div>
              <pre className="text-sm text-gray-700 whitespace-pre-wrap bg-white p-3 rounded border">
                {codeReviewFeedback}
              </pre>
            </div>
          </CardContent>
        )}

        {/* Visualization Content */}
        <CardContent className="p-6 pt-0">
          <AnimatePresence mode="wait" initial={false}>
            {showCode ? (
              <motion.div
                key="code-view"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="space-y-4"
              >
                <div className="bg-gray-900 rounded-lg p-4 overflow-auto max-h-96">
                  <pre className="text-sm text-gray-100">
                    <code>{visualization.code}</code>
                  </pre>
                </div>
              </motion.div>
            ) : (
              <motion.div
                key="visualization-view"
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.95 }}
                className="space-y-4"
              >
                {/* Rendered Visualization */}
                <Suspense fallback={
                  <div className="w-full h-96 bg-gradient-to-br from-gray-100 to-gray-200 rounded-lg flex items-center justify-center">
                    <div className="text-center">
                      <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                      <div className="text-gray-600">Loading visualization...</div>
                    </div>
                  </div>
                }>
                  <div className="min-h-[400px] w-full">
                    {VisualizationComponent}
                  </div>
                </Suspense>

                {/* Educational Notes */}
                {visualization.educationalNotes.length > 0 && (
                  <div className="bg-blue-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-2">
                      <Lightbulb className="w-4 h-4 text-blue-600" />
                      <h4 className="font-medium text-blue-900">Learning Points</h4>
                    </div>
                    <ul className="space-y-1 text-sm text-blue-800">
                      {visualization.educationalNotes.map((note, index) => (
                        <li key={index} className="flex items-start space-x-2">
                          <span className="text-blue-600 mt-1">•</span>
                          <span>{note}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Debug Response Display */}
      {debugResponse && (
        <Card className="mt-4">
          <CardContent className="p-4">
            <h4 className="font-semibold mb-2">🔍 Debug - Raw AI Response:</h4>
            <pre className="text-xs overflow-auto max-h-64 bg-gray-100 p-2 rounded border">
              {debugResponse}
            </pre>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setDebugResponse('')}
              className="mt-2"
            >
              Clear Debug
            </Button>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

export default DynamicVisualizationRenderer;
