/**
 * Physics Visualizations Component
 * Interactive physics simulations for various physics concepts
 */

import React, { useState, useEffect, useCallback, useRef } from 'react';
import { motion, useAnimation } from 'framer-motion';
import { <PERSON>, CardContent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { 
  Zap, 
  Play, 
  Pause, 
  RotateCcw,
  Settings,
  Activity,
  Target,
  ArrowRight
} from "lucide-react";
import { PhysicsVisualizationProps } from '../../../types';

interface PhysicsState {
  isPlaying: boolean;
  time: number;
  position: { x: number; y: number };
  velocity: { x: number; y: number };
  acceleration: { x: number; y: number };
  parameters: {
    gravity: number;
    initialVelocity: number;
    angle: number;
    mass: number;
    friction: number;
  };
}

export const PhysicsVisualizations: React.FC<PhysicsVisualizationProps> = ({
  topic,
  gradeLevel,
  difficulty = 'intermediate',
  physicsType,
  showVectors = true,
  showTrajectory = true,
  showForceLines = false,
  enableGravity = true,
  enableFriction = false,
  parameters = {},
  onParameterChange,
  className = ''
}) => {
  const animationRef = useRef<number>();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const ballAnimation = useAnimation();

  const [state, setState] = useState<PhysicsState>({
    isPlaying: false,
    time: 0,
    position: { x: 50, y: 300 },
    velocity: { x: 0, y: 0 },
    acceleration: { x: 0, y: 0 },
    parameters: {
      gravity: 9.8,
      initialVelocity: 20,
      angle: 45,
      mass: 1,
      friction: 0.1,
      ...parameters
    }
  });

  const [trajectory, setTrajectory] = useState<Array<{x: number, y: number}>>([]);

  // Physics simulation update
  const updatePhysics = useCallback((deltaTime: number) => {
    setState(prevState => {
      const { parameters, position, velocity } = prevState;
      const dt = deltaTime / 1000; // Convert to seconds

      let newAcceleration = { x: 0, y: 0 };
      let newVelocity = { ...velocity };
      let newPosition = { ...position };

      switch (physicsType) {
        case 'motion':
          // Projectile motion
          if (enableGravity) {
            newAcceleration.y = parameters.gravity;
          }
          if (enableFriction) {
            newAcceleration.x = -parameters.friction * velocity.x;
            newAcceleration.y += -parameters.friction * velocity.y;
          }
          break;

        case 'waves':
          // Simple harmonic motion
          const amplitude = 100;
          const frequency = parameters.initialVelocity / 10;
          newPosition.x = 200 + amplitude * Math.cos(frequency * prevState.time);
          newPosition.y = 200 + amplitude * Math.sin(frequency * prevState.time);
          break;

        case 'forces':
          // Force demonstration
          newAcceleration.x = parameters.initialVelocity * Math.cos(parameters.angle * Math.PI / 180) / parameters.mass;
          newAcceleration.y = parameters.initialVelocity * Math.sin(parameters.angle * Math.PI / 180) / parameters.mass;
          break;

        default:
          // Default projectile motion
          if (enableGravity) {
            newAcceleration.y = parameters.gravity;
          }
      }

      // Update velocity and position (Euler integration)
      if (physicsType !== 'waves') {
        newVelocity.x += newAcceleration.x * dt;
        newVelocity.y += newAcceleration.y * dt;
        newPosition.x += newVelocity.x * dt;
        newPosition.y += newVelocity.y * dt;
      }

      // Boundary conditions
      if (newPosition.y > 350) {
        newPosition.y = 350;
        newVelocity.y = -newVelocity.y * 0.8; // Bounce with energy loss
      }
      if (newPosition.x > 450 || newPosition.x < 0) {
        newVelocity.x = -newVelocity.x * 0.8;
      }

      return {
        ...prevState,
        time: prevState.time + dt,
        position: newPosition,
        velocity: newVelocity,
        acceleration: newAcceleration
      };
    });
  }, [physicsType, enableGravity, enableFriction]);

  // Animation loop
  useEffect(() => {
    let lastTime = 0;
    
    const animate = (currentTime: number) => {
      if (lastTime === 0) lastTime = currentTime;
      const deltaTime = currentTime - lastTime;
      lastTime = currentTime;

      if (state.isPlaying) {
        updatePhysics(deltaTime);
        
        // Update trajectory
        if (showTrajectory) {
          setTrajectory(prev => [...prev.slice(-50), { x: state.position.x, y: state.position.y }]);
        }
      }

      animationRef.current = requestAnimationFrame(animate);
    };

    animationRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [state.isPlaying, updatePhysics, showTrajectory, state.position]);

  const handlePlay = () => {
    setState(prev => ({ ...prev, isPlaying: true }));
  };

  const handlePause = () => {
    setState(prev => ({ ...prev, isPlaying: false }));
  };

  const handleReset = () => {
    setState(prev => ({
      ...prev,
      isPlaying: false,
      time: 0,
      position: { x: 50, y: 300 },
      velocity: { 
        x: prev.parameters.initialVelocity * Math.cos(prev.parameters.angle * Math.PI / 180),
        y: -prev.parameters.initialVelocity * Math.sin(prev.parameters.angle * Math.PI / 180)
      },
      acceleration: { x: 0, y: 0 }
    }));
    setTrajectory([]);
  };

  const handleParameterChange = (key: string, value: number) => {
    setState(prev => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [key]: value
      }
    }));

    if (onParameterChange) {
      onParameterChange(key, value);
    }
  };

  const getPhysicsTypeTitle = () => {
    const titles = {
      motion: 'Projectile Motion',
      waves: 'Wave Motion',
      forces: 'Forces and Acceleration',
      electricity: 'Electric Fields',
      quantum: 'Quantum Mechanics',
      thermodynamics: 'Thermodynamics'
    };
    return titles[physicsType] || 'Physics Simulation';
  };

  const getPhysicsTypeDescription = () => {
    const descriptions = {
      motion: 'Explore projectile motion with gravity and air resistance',
      waves: 'Visualize wave propagation and harmonic motion',
      forces: 'Understand forces, acceleration, and Newton\'s laws',
      electricity: 'See electric field lines and charge interactions',
      quantum: 'Explore quantum mechanical phenomena',
      thermodynamics: 'Understand heat transfer and molecular motion'
    };
    return descriptions[physicsType] || 'Interactive physics visualization';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Zap className="w-5 h-5 text-blue-500" />
              <div>
                <CardTitle className="text-lg">{getPhysicsTypeTitle()}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">{getPhysicsTypeDescription()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{gradeLevel}</Badge>
              <Badge variant="outline">{difficulty}</Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Simulation */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Simulation Canvas */}
            <div className="relative bg-gradient-to-b from-sky-100 to-green-100 rounded-lg h-80 overflow-hidden border-2 border-gray-200">
              {/* Trajectory */}
              {showTrajectory && (
                <svg className="absolute inset-0 w-full h-full pointer-events-none">
                  <path
                    d={trajectory.length > 1 ? 
                      `M ${trajectory[0].x} ${trajectory[0].y} ` + 
                      trajectory.slice(1).map(p => `L ${p.x} ${p.y}`).join(' ') : ''
                    }
                    stroke="#ef4444"
                    strokeWidth="2"
                    fill="none"
                    strokeDasharray="5,5"
                  />
                </svg>
              )}

              {/* Physics Object */}
              <motion.div
                className="absolute w-6 h-6 bg-blue-500 rounded-full shadow-lg"
                style={{
                  left: state.position.x - 12,
                  top: state.position.y - 12,
                }}
                animate={ballAnimation}
              />

              {/* Vectors */}
              {showVectors && (
                <div className="absolute inset-0 pointer-events-none">
                  {/* Velocity vector */}
                  <div
                    className="absolute w-1 bg-green-500"
                    style={{
                      left: state.position.x,
                      top: state.position.y,
                      height: Math.sqrt(state.velocity.x ** 2 + state.velocity.y ** 2) * 2,
                      transform: `rotate(${Math.atan2(state.velocity.y, state.velocity.x) * 180 / Math.PI}deg)`,
                      transformOrigin: 'top left'
                    }}
                  />
                  {/* Acceleration vector */}
                  <div
                    className="absolute w-1 bg-red-500"
                    style={{
                      left: state.position.x,
                      top: state.position.y,
                      height: Math.sqrt(state.acceleration.x ** 2 + state.acceleration.y ** 2) * 5,
                      transform: `rotate(${Math.atan2(state.acceleration.y, state.acceleration.x) * 180 / Math.PI}deg)`,
                      transformOrigin: 'top left'
                    }}
                  />
                </div>
              )}

              {/* Ground */}
              <div className="absolute bottom-0 left-0 right-0 h-8 bg-green-200 border-t-2 border-green-300" />
            </div>

            {/* Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Initial Velocity */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Initial Velocity</label>
                  <span className="text-sm text-gray-600">{state.parameters.initialVelocity} m/s</span>
                </div>
                <Slider
                  value={[state.parameters.initialVelocity]}
                  onValueChange={(value) => handleParameterChange('initialVelocity', value[0])}
                  min={5}
                  max={50}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Launch Angle */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Angle</label>
                  <span className="text-sm text-gray-600">{state.parameters.angle}°</span>
                </div>
                <Slider
                  value={[state.parameters.angle]}
                  onValueChange={(value) => handleParameterChange('angle', value[0])}
                  min={0}
                  max={90}
                  step={1}
                  className="w-full"
                />
              </div>

              {/* Gravity */}
              {enableGravity && (
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <label className="text-sm font-medium">Gravity</label>
                    <span className="text-sm text-gray-600">{state.parameters.gravity} m/s²</span>
                  </div>
                  <Slider
                    value={[state.parameters.gravity]}
                    onValueChange={(value) => handleParameterChange('gravity', value[0])}
                    min={0}
                    max={20}
                    step={0.1}
                    className="w-full"
                  />
                </div>
              )}

              {/* Mass */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Mass</label>
                  <span className="text-sm text-gray-600">{state.parameters.mass} kg</span>
                </div>
                <Slider
                  value={[state.parameters.mass]}
                  onValueChange={(value) => handleParameterChange('mass', value[0])}
                  min={0.1}
                  max={5}
                  step={0.1}
                  className="w-full"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-2 pt-4 border-t">
              <Button
                variant={state.isPlaying ? "secondary" : "default"}
                onClick={state.isPlaying ? handlePause : handlePlay}
                className="flex items-center space-x-2"
              >
                {state.isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{state.isPlaying ? 'Pause' : 'Start'}</span>
              </Button>
              
              <Button variant="outline" onClick={handleReset}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Physics Information */}
      <Card className="border-0 bg-blue-50">
        <CardContent className="p-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-900">
                {state.position.x.toFixed(1)} m
              </div>
              <div className="text-sm text-blue-700">Horizontal Position</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-900">
                {(400 - state.position.y).toFixed(1)} m
              </div>
              <div className="text-sm text-blue-700">Height</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-semibold text-blue-900">
                {Math.sqrt(state.velocity.x ** 2 + state.velocity.y ** 2).toFixed(1)} m/s
              </div>
              <div className="text-sm text-blue-700">Speed</div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
