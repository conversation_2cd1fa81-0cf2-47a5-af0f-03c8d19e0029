import { AIModelOption, DifficultyLevel, CareerField } from './types';

// AI Models for career analysis
export const AI_MODELS: AIModelOption[] = [
  {
    id: 'gpt-4-turbo',
    name: 'GPT-4 Turbo',
    provider: 'OpenAI',
    capabilities: ['career-analysis', 'roadmap-generation', 'skill-assessment'],
    maxTokens: 4096
  },
  {
    id: 'claude-3-sonnet',
    name: 'Claude 3 Sonnet',
    provider: 'Anthropic',
    capabilities: ['career-analysis', 'detailed-planning', 'industry-insights'],
    maxTokens: 4096
  },
  {
    id: 'gemini-pro',
    name: 'Gemini Pro',
    provider: 'Google',
    capabilities: ['career-analysis', 'market-research', 'skill-matching'],
    maxTokens: 4096
  }
];

// Career difficulty levels with styling
export const DIFFICULTY_LEVELS: Record<DifficultyLevel, { color: string; description: string }> = {
  'Low': {
    color: 'text-green-600',
    description: 'Easy transition with existing skills'
  },
  'Medium': {
    color: 'text-orange-600',
    description: 'Moderate learning curve required'
  },
  'High': {
    color: 'text-red-600',
    description: 'Significant skill development needed'
  }
};

// Career fields and categories
export const CAREER_FIELDS: CareerField[] = [
  'Technology',
  'Healthcare',
  'Finance',
  'Education',
  'Marketing',
  'Design',
  'Other'
];

// Default career visualization layout
export const VISUALIZATION_CONFIG = {
  centerNode: {
    position: { x: 650, y: 450 },
    style: {
      background: 'linear-gradient(135deg, #3B82F6 0%, #1E40AF 100%)',
      color: '#fff',
      fontSize: '18px',
      fontWeight: 'bold',
      borderRadius: '12px',
      padding: '16px 24px',
      border: 'none',
      boxShadow: '0 8px 32px rgba(59, 130, 246, 0.3)'
    }
  },
  careerNodePositions: [
    { x: 50, y: 550 },   // Bottom left
    { x: 1050, y: 550 }, // Bottom right
    { x: 50, y: 150 },   // Top left
    { x: 1050, y: 150 }, // Top right
    { x: 550, y: 700 },  // Bottom center
    { x: 550, y: 0 }     // Top center
  ],
  edgeStyle: {
    stroke: '#3B82F6',
    strokeWidth: 2,
    strokeDasharray: '5,5'
  }
};

// Export format options
export const EXPORT_FORMATS = [
  {
    id: 'pdf',
    name: 'PDF Report',
    description: 'Comprehensive career analysis report',
    icon: 'FileText'
  },
  {
    id: 'docx',
    name: 'Word Document',
    description: 'Editable career roadmap document',
    icon: 'FileEdit'
  },
  {
    id: 'json',
    name: 'JSON Data',
    description: 'Raw data for further analysis',
    icon: 'Database'
  }
];

// Default generation options
export const DEFAULT_GENERATION_OPTIONS = {
  model: 'gpt-4-turbo',
  temperature: 0.7,
  maxTokens: 4096,
  numberOfCareers: 6,
  includeRoadmaps: true,
  includeSkillAnalysis: true
};

// Career analysis prompts
export const CAREER_PROMPTS = {
  initialAnalysis: `You are a professional career counselor and expert in career transitions. Analyze the provided resume and additional context to suggest 6 personalized career paths.

For each career path, provide:
- jobTitle: Clear, specific job title
- jobDescription: Concise description (1-2 sentences)
- timeline: Realistic transition timeframe
- salary: Expected salary range
- difficulty: Low/Medium/High based on skill gap

Respond ONLY with valid JSON in this exact format:
[
  {
    "jobTitle": "string",
    "jobDescription": "string", 
    "timeline": "string",
    "salary": "string",
    "difficulty": "Low|Medium|High"
  }
]`,

  detailedAnalysis: `You are helping someone transition into the {jobTitle} role. Based on their background, provide detailed career guidance.

Respond ONLY with valid JSON in this exact format:
{
  "workRequired": "string (hours per week for preparation)",
  "aboutTheRole": "string (detailed role description)",
  "whyItsGoodFit": ["string", "string", "string"] (3-4 reasons),
  "roadmap": [
    {"timeframe": "description of what to do in this period"},
    {"timeframe": "description of what to do in this period"}
  ],
  "skills": ["skill1", "skill2", "skill3"],
  "certifications": ["cert1", "cert2"]
}`
};

// File upload configuration
export const UPLOAD_CONFIG = {
  maxFileSize: 10 * 1024 * 1024, // 10MB
  acceptedTypes: ['.pdf', '.doc', '.docx', '.txt'],
  acceptedMimeTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'text/plain'
  ]
};

// UI Constants
export const UI_CONSTANTS = {
  animationDuration: 300,
  debounceDelay: 500,
  maxResumeTextLength: 50000,
  maxContextLength: 2000,
  defaultTimeout: 30000
};

// Color scheme for career visualization
export const CAREER_COLORS = {
  primary: '#3B82F6',
  secondary: '#1E40AF', 
  success: '#10B981',
  warning: '#F59E0B',
  danger: '#EF4444',
  muted: '#6B7280',
  background: '#F8FAFC',
  cardBackground: '#FFFFFF'
};

// Default career categories for suggestions
export const CAREER_CATEGORIES = {
  'Technology': ['Software Engineer', 'Data Scientist', 'UX Designer', 'DevOps Engineer'],
  'Healthcare': ['Nurse Practitioner', 'Medical Technician', 'Healthcare Administrator'],
  'Finance': ['Financial Analyst', 'Investment Advisor', 'Risk Manager'],
  'Education': ['Instructional Designer', 'Corporate Trainer', 'Educational Consultant'],
  'Marketing': ['Digital Marketing Specialist', 'Content Strategist', 'Brand Manager'],
  'Design': ['Graphic Designer', 'Product Designer', 'Creative Director']
};
