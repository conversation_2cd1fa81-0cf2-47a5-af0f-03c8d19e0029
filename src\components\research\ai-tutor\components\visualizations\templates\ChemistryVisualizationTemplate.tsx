/**
 * Chemistry Visualization Template
 * Interactive chemistry simulations and molecular models
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Play, Pause, RotateCcw, Settings, Atom, Beaker } from "lucide-react";

interface ChemistryVisualizationProps {
  type: 'molecule' | 'reaction' | 'bonding' | 'states' | 'equilibrium' | 'ph';
  title: string;
  description: string;
  educationLevel: 'elementary' | 'middle-school' | 'high-school' | 'college';
  onParameterChange?: (parameter: string, value: number) => void;
}

interface ChemistryState {
  isAnimating: boolean;
  time: number;
  parameters: Record<string, number>;
  selectedMolecule: string;
  reactionProgress: number;
  temperature: number;
  pressure: number;
}

interface Atom {
  id: string;
  element: string;
  x: number;
  y: number;
  z: number;
  color: string;
  radius: number;
  bonds: string[];
}

interface Bond {
  id: string;
  atom1: string;
  atom2: string;
  type: 'single' | 'double' | 'triple';
  strength: number;
}

export const ChemistryVisualizationTemplate: React.FC<ChemistryVisualizationProps> = ({
  type,
  title,
  description,
  educationLevel,
  onParameterChange
}) => {
  const [chemState, setChemState] = useState<ChemistryState>({
    isAnimating: false,
    time: 0,
    parameters: getDefaultParameters(type),
    selectedMolecule: 'water',
    reactionProgress: 0,
    temperature: 298,
    pressure: 1
  });
  
  const [showControls, setShowControls] = useState(false);
  const [atoms, setAtoms] = useState<Atom[]>([]);
  const [bonds, setBonds] = useState<Bond[]>([]);

  // Initialize molecule structure
  useEffect(() => {
    const { atoms: newAtoms, bonds: newBonds } = getMoleculeStructure(chemState.selectedMolecule);
    setAtoms(newAtoms);
    setBonds(newBonds);
  }, [chemState.selectedMolecule]);

  // Animation loop
  useEffect(() => {
    if (chemState.isAnimating) {
      const interval = setInterval(() => {
        setChemState(prev => ({
          ...prev,
          time: prev.time + 0.1,
          reactionProgress: Math.min(prev.reactionProgress + 0.01, 1)
        }));
      }, 50);
      return () => clearInterval(interval);
    }
  }, [chemState.isAnimating]);

  const toggleAnimation = () => {
    setChemState(prev => ({ ...prev, isAnimating: !prev.isAnimating }));
  };

  const resetVisualization = () => {
    setChemState(prev => ({ 
      ...prev, 
      time: 0, 
      isAnimating: false, 
      reactionProgress: 0 
    }));
  };

  const updateParameter = (parameter: string, value: number[]) => {
    const newValue = value[0];
    setChemState(prev => ({
      ...prev,
      parameters: { ...prev.parameters, [parameter]: newValue },
      [parameter]: newValue
    }));
    onParameterChange?.(parameter, newValue);
  };

  const renderVisualization = () => {
    switch (type) {
      case 'molecule':
        return <MoleculeViewer atoms={atoms} bonds={bonds} chemState={chemState} />;
      case 'reaction':
        return <ReactionAnimation chemState={chemState} />;
      case 'bonding':
        return <BondingVisualization chemState={chemState} />;
      case 'states':
        return <StatesOfMatter chemState={chemState} />;
      case 'equilibrium':
        return <EquilibriumVisualization chemState={chemState} />;
      case 'ph':
        return <PHVisualization chemState={chemState} />;
      default:
        return <MoleculeViewer atoms={atoms} bonds={bonds} chemState={chemState} />;
    }
  };

  const renderControls = () => {
    const params = getParameterConfig(type, educationLevel);
    
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900">Chemistry Controls</h4>
        
        {type === 'molecule' && (
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Molecule</label>
            <Select
              value={chemState.selectedMolecule}
              onValueChange={(value) => setChemState(prev => ({ ...prev, selectedMolecule: value }))}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="water">Water (H₂O)</SelectItem>
                <SelectItem value="methane">Methane (CH₄)</SelectItem>
                <SelectItem value="ammonia">Ammonia (NH₃)</SelectItem>
                <SelectItem value="carbon_dioxide">Carbon Dioxide (CO₂)</SelectItem>
                <SelectItem value="benzene">Benzene (C₆H₆)</SelectItem>
              </SelectContent>
            </Select>
          </div>
        )}
        
        {params.map((param) => (
          <div key={param.key} className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium text-gray-700">
                {param.label}
              </label>
              <span className="text-sm text-gray-500">
                {chemState.parameters[param.key]?.toFixed(1)} {param.unit}
              </span>
            </div>
            <Slider
              value={[chemState.parameters[param.key] || param.default]}
              onValueChange={(value) => updateParameter(param.key, value)}
              min={param.min}
              max={param.max}
              step={param.step}
              className="w-full"
            />
            <p className="text-xs text-gray-600">{param.description}</p>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Beaker className="w-5 h-5 text-purple-500" />
              <span>{title}</span>
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{type}</Badge>
            <Badge variant="outline">{educationLevel}</Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Chemistry Visualization Display */}
        <div className="w-full h-80 bg-gradient-to-br from-purple-50 to-pink-50 rounded-lg relative overflow-hidden border-2 border-purple-200">
          {renderVisualization()}
          
          {/* Control Overlay */}
          <div className="absolute top-4 left-4 flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleAnimation}
              className="bg-white/90 backdrop-blur-sm"
            >
              {chemState.isAnimating ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={resetVisualization}
              className="bg-white/90 backdrop-blur-sm"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowControls(!showControls)}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {/* Status Display */}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
            <div className="text-sm">
              {type === 'reaction' && (
                <span>Progress: {(chemState.reactionProgress * 100).toFixed(0)}%</span>
              )}
              {type === 'states' && (
                <span>T: {chemState.temperature}K</span>
              )}
              {type === 'molecule' && (
                <span>{getMoleculeFormula(chemState.selectedMolecule)}</span>
              )}
            </div>
          </div>
        </div>

        {/* Interactive Controls */}
        {showControls && renderControls()}

        {/* Chemical Concepts */}
        <div className="bg-purple-50 rounded-lg p-4">
          <h4 className="font-medium text-purple-900 mb-2">Chemical Concepts</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium text-purple-800 mb-1">Key Concepts:</h5>
              <ul className="text-purple-700 space-y-1">
                {getKeyChemConcepts(type, educationLevel).map((concept, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span>{concept}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-purple-800 mb-1">Observations:</h5>
              <ul className="text-purple-700 space-y-1">
                {getObservations(type, chemState).map((observation, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-purple-600 mt-1">•</span>
                    <span>{observation}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Visualization Components
const MoleculeViewer: React.FC<{ 
  atoms: Atom[], 
  bonds: Bond[], 
  chemState: ChemistryState 
}> = ({ atoms, bonds, chemState }) => {
  return (
    <div className="w-full h-full relative">
      <svg className="absolute inset-0 w-full h-full">
        {/* Bonds */}
        {bonds.map((bond) => {
          const atom1 = atoms.find(a => a.id === bond.atom1);
          const atom2 = atoms.find(a => a.id === bond.atom2);
          if (!atom1 || !atom2) return null;
          
          return (
            <g key={bond.id}>
              <line
                x1={atom1.x} y1={atom1.y}
                x2={atom2.x} y2={atom2.y}
                stroke="#6b7280"
                strokeWidth={bond.type === 'single' ? 2 : bond.type === 'double' ? 4 : 6}
                className="drop-shadow-sm"
              />
              {bond.type === 'double' && (
                <line
                  x1={atom1.x + 2} y1={atom1.y + 2}
                  x2={atom2.x + 2} y2={atom2.y + 2}
                  stroke="#6b7280"
                  strokeWidth="2"
                />
              )}
            </g>
          );
        })}
        
        {/* Atoms */}
        {atoms.map((atom) => (
          <g key={atom.id}>
            <motion.circle
              cx={atom.x}
              cy={atom.y}
              r={atom.radius}
              fill={atom.color}
              stroke="#374151"
              strokeWidth="2"
              className="drop-shadow-lg"
              animate={chemState.isAnimating ? {
                scale: [1, 1.1, 1],
                rotate: [0, 360]
              } : {}}
              transition={{
                duration: 2,
                repeat: chemState.isAnimating ? Infinity : 0,
                ease: "easeInOut"
              }}
            />
            <text
              x={atom.x}
              y={atom.y + 4}
              textAnchor="middle"
              className="text-xs font-bold fill-white"
              style={{ textShadow: '1px 1px 1px rgba(0,0,0,0.5)' }}
            >
              {atom.element}
            </text>
          </g>
        ))}
      </svg>
    </div>
  );
};

const ReactionAnimation: React.FC<{ chemState: ChemistryState }> = ({ chemState }) => {
  const progress = chemState.reactionProgress;
  
  return (
    <div className="w-full h-full relative flex items-center justify-center">
      {/* Reactants */}
      <div className="flex items-center space-x-4">
        <motion.div
          className="flex items-center space-x-2"
          animate={{ x: progress * 50 }}
        >
          <div className="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-xs font-bold">
            H₂
          </div>
        </motion.div>
        
        <span className="text-xl font-bold">+</span>
        
        <motion.div
          className="flex items-center space-x-2"
          animate={{ x: -progress * 50 }}
        >
          <div className="w-8 h-8 bg-red-400 rounded-full flex items-center justify-center text-white text-xs font-bold">
            O₂
          </div>
        </motion.div>
      </div>
      
      {/* Arrow */}
      <div className="mx-8">
        <motion.div
          className="text-2xl"
          animate={{ scale: progress > 0.5 ? [1, 1.2, 1] : 1 }}
          transition={{ duration: 0.5, repeat: progress > 0.5 ? Infinity : 0 }}
        >
          →
        </motion.div>
      </div>
      
      {/* Products */}
      <AnimatePresence>
        {progress > 0.3 && (
          <motion.div
            initial={{ opacity: 0, scale: 0 }}
            animate={{ opacity: 1, scale: 1 }}
            className="flex items-center space-x-2"
          >
            <div className="w-10 h-8 bg-green-400 rounded-lg flex items-center justify-center text-white text-xs font-bold">
              H₂O
            </div>
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Energy Diagram */}
      <div className="absolute bottom-4 left-4 right-4">
        <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2">
          <div className="h-2 bg-gray-200 rounded-full overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-blue-500 to-green-500"
              initial={{ width: 0 }}
              animate={{ width: `${progress * 100}%` }}
            />
          </div>
          <p className="text-xs text-center mt-1">Reaction Progress</p>
        </div>
      </div>
    </div>
  );
};

const BondingVisualization: React.FC<{ chemState: ChemistryState }> = ({ chemState }) => {
  return (
    <div className="w-full h-full relative flex items-center justify-center">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">Ionic vs Covalent Bonding</h3>
        <div className="flex items-center space-x-8">
          {/* Ionic Bonding */}
          <div className="text-center">
            <h4 className="font-medium mb-2">Ionic</h4>
            <div className="flex items-center space-x-2">
              <motion.div
                className="w-8 h-8 bg-red-400 rounded-full flex items-center justify-center text-white text-xs"
                animate={chemState.isAnimating ? { x: [0, -10, 0] } : {}}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Na⁺
              </motion.div>
              <motion.div
                className="w-8 h-8 bg-green-400 rounded-full flex items-center justify-center text-white text-xs"
                animate={chemState.isAnimating ? { x: [0, 10, 0] } : {}}
                transition={{ duration: 2, repeat: Infinity }}
              >
                Cl⁻
              </motion.div>
            </div>
          </div>
          
          {/* Covalent Bonding */}
          <div className="text-center">
            <h4 className="font-medium mb-2">Covalent</h4>
            <div className="flex items-center">
              <div className="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-xs">
                H
              </div>
              <div className="w-4 h-1 bg-gray-400"></div>
              <div className="w-8 h-8 bg-blue-400 rounded-full flex items-center justify-center text-white text-xs">
                H
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const StatesOfMatter: React.FC<{ chemState: ChemistryState }> = ({ chemState }) => {
  const { temperature } = chemState;
  let state = 'solid';
  if (temperature > 273) state = 'liquid';
  if (temperature > 373) state = 'gas';
  
  const particleCount = 12;
  const particles = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    x: 50 + (i % 4) * 80,
    y: 100 + Math.floor(i / 4) * 60
  }));
  
  return (
    <div className="w-full h-full relative">
      <div className="absolute top-4 left-4">
        <h4 className="font-medium">State: {state.charAt(0).toUpperCase() + state.slice(1)}</h4>
      </div>
      
      {particles.map((particle) => (
        <motion.div
          key={particle.id}
          className="absolute w-4 h-4 bg-blue-500 rounded-full"
          initial={{ x: particle.x, y: particle.y }}
          animate={
            state === 'solid' ? {
              x: particle.x + [-2, 2, -2, 2][Math.floor(Math.random() * 4)],
              y: particle.y + [-2, 2, -2, 2][Math.floor(Math.random() * 4)]
            } : state === 'liquid' ? {
              x: particle.x + (Math.random() - 0.5) * 40,
              y: particle.y + (Math.random() - 0.5) * 40
            } : {
              x: Math.random() * 350,
              y: Math.random() * 250
            }
          }
          transition={{
            duration: state === 'solid' ? 0.5 : state === 'liquid' ? 1 : 2,
            repeat: Infinity,
            repeatType: 'reverse'
          }}
        />
      ))}
    </div>
  );
};

const EquilibriumVisualization: React.FC<{ chemState: ChemistryState }> = ({ chemState }) => {
  return (
    <div className="w-full h-full relative flex items-center justify-center">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">Chemical Equilibrium</h3>
        <div className="flex items-center space-x-4">
          <div className="text-blue-600 font-bold">A + B</div>
          <div className="flex flex-col items-center">
            <div className="text-sm">⇌</div>
            <div className="text-xs">K = {chemState.parameters.equilibrium?.toFixed(2) || '1.00'}</div>
          </div>
          <div className="text-green-600 font-bold">C + D</div>
        </div>
      </div>
    </div>
  );
};

const PHVisualization: React.FC<{ chemState: ChemistryState }> = ({ chemState }) => {
  const ph = chemState.parameters.ph || 7;
  const color = ph < 7 ? '#ef4444' : ph > 7 ? '#3b82f6' : '#10b981';
  const label = ph < 7 ? 'Acidic' : ph > 7 ? 'Basic' : 'Neutral';
  
  return (
    <div className="w-full h-full relative flex items-center justify-center">
      <div className="text-center">
        <h3 className="text-lg font-semibold mb-4">pH Scale</h3>
        <div className="w-64 h-8 bg-gradient-to-r from-red-500 via-green-500 to-blue-500 rounded-lg relative">
          <motion.div
            className="absolute top-0 w-2 h-8 bg-white border-2 border-gray-800 rounded"
            style={{ left: `${(ph / 14) * 100}%` }}
            animate={{ x: -4 }}
          />
        </div>
        <div className="flex justify-between text-xs mt-2">
          <span>0 (Acidic)</span>
          <span>7 (Neutral)</span>
          <span>14 (Basic)</span>
        </div>
        <div className="mt-4">
          <div className="text-2xl font-bold" style={{ color }}>
            pH: {ph.toFixed(1)}
          </div>
          <div className="text-sm text-gray-600">{label}</div>
        </div>
      </div>
    </div>
  );
};

// Helper Functions
function getDefaultParameters(type: string): Record<string, number> {
  switch (type) {
    case 'states':
      return { temperature: 298 };
    case 'equilibrium':
      return { equilibrium: 1.0, temperature: 298 };
    case 'ph':
      return { ph: 7.0 };
    default:
      return {};
  }
}

function getMoleculeStructure(molecule: string): { atoms: Atom[], bonds: Bond[] } {
  const structures = {
    water: {
      atoms: [
        { id: 'O1', element: 'O', x: 200, y: 150, z: 0, color: '#ef4444', radius: 16, bonds: ['H1', 'H2'] },
        { id: 'H1', element: 'H', x: 170, y: 180, z: 0, color: '#f3f4f6', radius: 8, bonds: ['O1'] },
        { id: 'H2', element: 'H', x: 230, y: 180, z: 0, color: '#f3f4f6', radius: 8, bonds: ['O1'] }
      ],
      bonds: [
        { id: 'b1', atom1: 'O1', atom2: 'H1', type: 'single' as const, strength: 1 },
        { id: 'b2', atom1: 'O1', atom2: 'H2', type: 'single' as const, strength: 1 }
      ]
    },
    methane: {
      atoms: [
        { id: 'C1', element: 'C', x: 200, y: 150, z: 0, color: '#374151', radius: 14, bonds: ['H1', 'H2', 'H3', 'H4'] },
        { id: 'H1', element: 'H', x: 170, y: 120, z: 0, color: '#f3f4f6', radius: 8, bonds: ['C1'] },
        { id: 'H2', element: 'H', x: 230, y: 120, z: 0, color: '#f3f4f6', radius: 8, bonds: ['C1'] },
        { id: 'H3', element: 'H', x: 170, y: 180, z: 0, color: '#f3f4f6', radius: 8, bonds: ['C1'] },
        { id: 'H4', element: 'H', x: 230, y: 180, z: 0, color: '#f3f4f6', radius: 8, bonds: ['C1'] }
      ],
      bonds: [
        { id: 'b1', atom1: 'C1', atom2: 'H1', type: 'single' as const, strength: 1 },
        { id: 'b2', atom1: 'C1', atom2: 'H2', type: 'single' as const, strength: 1 },
        { id: 'b3', atom1: 'C1', atom2: 'H3', type: 'single' as const, strength: 1 },
        { id: 'b4', atom1: 'C1', atom2: 'H4', type: 'single' as const, strength: 1 }
      ]
    }
  };
  
  return structures[molecule] || structures.water;
}

function getMoleculeFormula(molecule: string): string {
  const formulas = {
    water: 'H₂O',
    methane: 'CH₄',
    ammonia: 'NH₃',
    carbon_dioxide: 'CO₂',
    benzene: 'C₆H₆'
  };
  
  return formulas[molecule] || 'Unknown';
}

function getParameterConfig(type: string, educationLevel: string) {
  const configs = {
    states: [
      { key: 'temperature', label: 'Temperature', min: 200, max: 500, step: 10, default: 298, unit: 'K', description: 'Temperature affects molecular motion' }
    ],
    ph: [
      { key: 'ph', label: 'pH Level', min: 0, max: 14, step: 0.1, default: 7, unit: '', description: 'Measure of acidity/basicity' }
    ]
  };
  
  return configs[type] || [];
}

function getKeyChemConcepts(type: string, educationLevel: string): string[] {
  const concepts = {
    molecule: ['Atoms bond to form molecules', 'Different elements have different properties'],
    reaction: ['Reactants combine to form products', 'Energy is involved in chemical reactions'],
    states: ['Temperature affects molecular motion', 'Matter exists in different states']
  };
  
  return concepts[type] || ['Chemical interactions follow predictable patterns'];
}

function getObservations(type: string, chemState: ChemistryState): string[] {
  switch (type) {
    case 'states':
      if (chemState.temperature < 273) return ['Particles vibrate in fixed positions', 'Solid state - low energy'];
      if (chemState.temperature < 373) return ['Particles move more freely', 'Liquid state - medium energy'];
      return ['Particles move rapidly in all directions', 'Gas state - high energy'];
    case 'ph':
      const ph = chemState.parameters.ph || 7;
      if (ph < 7) return ['Red color indicates acidity', 'H⁺ ions are abundant'];
      if (ph > 7) return ['Blue color indicates basicity', 'OH⁻ ions are abundant'];
      return ['Green color indicates neutrality', 'Equal H⁺ and OH⁻ ions'];
    default:
      return ['Observe how parameters affect the system'];
  }
}

export default ChemistryVisualizationTemplate;
