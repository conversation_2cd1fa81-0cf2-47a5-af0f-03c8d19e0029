/**
 * Educational Visualizations Index
 * Exports all visualization components and utilities
 */

// Core visualization framework
export { EducationalVisualization } from './EducationalVisualization';
export { AnimationControlsComponent } from './AnimationControls';
export { GradeLevelAdapter } from './GradeLevelAdapter';

// Subject-specific visualizations
export { MathVisualizations } from './subjects/MathVisualizations';
export { PhysicsVisualizations } from './subjects/PhysicsVisualizations';
export { ChemistryVisualizations } from './subjects/ChemistryVisualizations';

// Visualization registry for dynamic loading
import { ComponentType } from 'react';
import { 
  Subject, 
  EducationalVisualizationProps,
  MathVisualizationProps,
  PhysicsVisualizationProps,
  ChemistryVisualizationProps
} from '../../types';

import { MathVisualizations } from './subjects/MathVisualizations';
import { PhysicsVisualizations } from './subjects/PhysicsVisualizations';
import { ChemistryVisualizations } from './subjects/ChemistryVisualizations';

// Visualization component registry
export const VISUALIZATION_REGISTRY: {
  [key in Subject]: {
    component: ComponentType<any>;
    topics: string[];
    description: string;
  }
} = {
  mathematics: {
    component: MathVisualizations,
    topics: [
      'functions', 'equations', 'graphs', 'derivatives', 'integrals', 
      'trigonometry', 'algebra', 'calculus', 'geometry', 'statistics'
    ],
    description: 'Interactive mathematical visualizations and function plotting'
  },
  physics: {
    component: PhysicsVisualizations,
    topics: [
      'motion', 'forces', 'energy', 'waves', 'electricity', 'magnetism',
      'thermodynamics', 'quantum mechanics', 'relativity', 'optics'
    ],
    description: 'Physics simulations and interactive demonstrations'
  },
  chemistry: {
    component: ChemistryVisualizations,
    topics: [
      'molecules', 'atoms', 'bonds', 'reactions', 'periodic table',
      'molecular structure', 'chemical bonding', 'electron configuration'
    ],
    description: '3D molecular visualizations and chemical concepts'
  },
  biology: {
    component: MathVisualizations, // Placeholder - would be BiologyVisualizations
    topics: [
      'cells', 'dna', 'proteins', 'evolution', 'ecosystems',
      'genetics', 'anatomy', 'physiology', 'molecular biology'
    ],
    description: 'Biological processes and anatomical visualizations'
  },
  'computer-science': {
    component: MathVisualizations, // Placeholder - would be CSVisualizations
    topics: [
      'algorithms', 'data structures', 'sorting', 'searching',
      'graphs', 'trees', 'networks', 'machine learning'
    ],
    description: 'Algorithm visualizations and computer science concepts'
  },
  engineering: {
    component: PhysicsVisualizations, // Placeholder - would be EngineeringVisualizations
    topics: [
      'circuits', 'systems', 'signals', 'control systems',
      'mechanics', 'thermodynamics', 'fluid dynamics'
    ],
    description: 'Engineering systems and technical visualizations'
  },
  economics: {
    component: MathVisualizations, // Placeholder - would be EconomicsVisualizations
    topics: [
      'supply and demand', 'market equilibrium', 'elasticity',
      'game theory', 'macroeconomics', 'microeconomics'
    ],
    description: 'Economic models and market visualizations'
  },
  general: {
    component: MathVisualizations, // Placeholder - would be GeneralVisualizations
    topics: [
      'concepts', 'processes', 'relationships', 'comparisons',
      'timelines', 'hierarchies', 'flowcharts'
    ],
    description: 'General conceptual visualizations and diagrams'
  }
};

/**
 * Get visualization component for a subject
 */
export function getVisualizationComponent(subject: Subject): ComponentType<any> | null {
  const registry = VISUALIZATION_REGISTRY[subject];
  return registry ? registry.component : null;
}

/**
 * Check if a topic is supported for visualization in a subject
 */
export function isTopicSupported(subject: Subject, topic: string): boolean {
  const registry = VISUALIZATION_REGISTRY[subject];
  if (!registry) return false;
  
  const normalizedTopic = topic.toLowerCase();
  return registry.topics.some(supportedTopic => 
    normalizedTopic.includes(supportedTopic) || supportedTopic.includes(normalizedTopic)
  );
}

/**
 * Get all supported topics for a subject
 */
export function getSupportedTopics(subject: Subject): string[] {
  const registry = VISUALIZATION_REGISTRY[subject];
  return registry ? registry.topics : [];
}

/**
 * Get visualization description for a subject
 */
export function getVisualizationDescription(subject: Subject): string {
  const registry = VISUALIZATION_REGISTRY[subject];
  return registry ? registry.description : 'Interactive educational visualization';
}

/**
 * Get all available subjects with visualizations
 */
export function getAvailableSubjects(): Subject[] {
  return Object.keys(VISUALIZATION_REGISTRY) as Subject[];
}

/**
 * Render appropriate visualization component based on subject and topic
 */
export function renderVisualization(
  subject: Subject,
  topic: string,
  props: Partial<EducationalVisualizationProps>
): JSX.Element | null {
  const Component = getVisualizationComponent(subject);
  
  if (!Component) {
    return null;
  }

  // Map subject to specific props
  const subjectProps = getSubjectSpecificProps(subject, topic);
  
  return (
    <Component
      topic={topic}
      subject={subject}
      {...subjectProps}
      {...props}
    />
  );
}

/**
 * Get subject-specific props based on subject and topic
 */
function getSubjectSpecificProps(subject: Subject, topic: string): any {
  const normalizedTopic = topic.toLowerCase();
  
  switch (subject) {
    case 'mathematics':
      // Determine math type based on topic
      let mathType: MathVisualizationProps['mathType'] = 'algebra';
      if (normalizedTopic.includes('calculus') || normalizedTopic.includes('derivative') || normalizedTopic.includes('integral')) {
        mathType = 'calculus';
      } else if (normalizedTopic.includes('trigonometry') || normalizedTopic.includes('sine') || normalizedTopic.includes('cosine')) {
        mathType = 'trigonometry';
      } else if (normalizedTopic.includes('geometry') || normalizedTopic.includes('shape') || normalizedTopic.includes('circle')) {
        mathType = 'geometry';
      } else if (normalizedTopic.includes('statistics') || normalizedTopic.includes('probability')) {
        mathType = 'statistics';
      } else if (normalizedTopic.includes('linear') || normalizedTopic.includes('matrix')) {
        mathType = 'linear-algebra';
      }
      
      return {
        mathType,
        showGrid: true,
        showAxes: true,
        showDerivative: mathType === 'calculus',
        animateTransformation: true
      };

    case 'physics':
      // Determine physics type based on topic
      let physicsType: PhysicsVisualizationProps['physicsType'] = 'motion';
      if (normalizedTopic.includes('wave') || normalizedTopic.includes('oscillation')) {
        physicsType = 'waves';
      } else if (normalizedTopic.includes('force') || normalizedTopic.includes('newton')) {
        physicsType = 'forces';
      } else if (normalizedTopic.includes('electric') || normalizedTopic.includes('magnetic')) {
        physicsType = 'electricity';
      } else if (normalizedTopic.includes('quantum')) {
        physicsType = 'quantum';
      } else if (normalizedTopic.includes('heat') || normalizedTopic.includes('temperature')) {
        physicsType = 'thermodynamics';
      }
      
      return {
        physicsType,
        showVectors: true,
        showTrajectory: physicsType === 'motion',
        enableGravity: physicsType === 'motion',
        enableFriction: false
      };

    case 'chemistry':
      // Determine chemistry type based on topic
      let chemistryType: ChemistryVisualizationProps['chemistryType'] = 'molecular-structure';
      if (normalizedTopic.includes('reaction') || normalizedTopic.includes('chemical reaction')) {
        chemistryType = 'reactions';
      } else if (normalizedTopic.includes('bond') || normalizedTopic.includes('bonding')) {
        chemistryType = 'bonding';
      } else if (normalizedTopic.includes('periodic') || normalizedTopic.includes('element')) {
        chemistryType = 'periodic-trends';
      }
      
      return {
        chemistryType,
        show3D: true,
        showBonds: true,
        enableRotation: true,
        showElectrons: false
      };

    default:
      return {};
  }
}

// Performance optimization utilities
export const VISUALIZATION_PERFORMANCE = {
  // Maximum number of animation frames per second
  MAX_FPS: 60,
  
  // Debounce delay for parameter changes (ms)
  PARAMETER_DEBOUNCE: 100,
  
  // Maximum number of data points for charts
  MAX_DATA_POINTS: 1000,
  
  // Animation frame throttling
  FRAME_THROTTLE: 16.67, // ~60fps
  
  // Memory cleanup interval (ms)
  CLEANUP_INTERVAL: 30000, // 30 seconds
};

/**
 * Throttle animation frames for performance
 */
export function throttleAnimationFrame(callback: () => void): number {
  let lastTime = 0;
  
  const throttledCallback = (currentTime: number) => {
    if (currentTime - lastTime >= VISUALIZATION_PERFORMANCE.FRAME_THROTTLE) {
      callback();
      lastTime = currentTime;
    }
    return requestAnimationFrame(throttledCallback);
  };
  
  return requestAnimationFrame(throttledCallback);
}

/**
 * Debounce parameter changes for performance
 */
export function debounceParameterChange(
  callback: (key: string, value: any) => void,
  delay: number = VISUALIZATION_PERFORMANCE.PARAMETER_DEBOUNCE
) {
  let timeoutId: NodeJS.Timeout;
  
  return (key: string, value: any) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => callback(key, value), delay);
  };
}
