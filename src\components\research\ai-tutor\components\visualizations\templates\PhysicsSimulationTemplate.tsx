/**
 * Physics Simulation Template
 * Interactive physics simulations for educational purposes
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Play, Pause, RotateCcw, Settings } from "lucide-react";

interface PhysicsSimulationProps {
  type: 'projectile' | 'pendulum' | 'collision' | 'wave' | 'spring';
  title: string;
  description: string;
  educationLevel: 'elementary' | 'middle-school' | 'high-school' | 'college';
  onParameterChange?: (parameter: string, value: number) => void;
}

interface SimulationState {
  isPlaying: boolean;
  time: number;
  objects: PhysicsObject[];
  parameters: Record<string, number>;
}

interface PhysicsObject {
  id: string;
  x: number;
  y: number;
  vx: number;
  vy: number;
  mass: number;
  radius: number;
  color: string;
  trail: { x: number; y: number }[];
}

export const PhysicsSimulationTemplate: React.FC<PhysicsSimulationProps> = ({
  type,
  title,
  description,
  educationLevel,
  onParameterChange
}) => {
  const [simulation, setSimulation] = useState<SimulationState>({
    isPlaying: false,
    time: 0,
    objects: [],
    parameters: getDefaultParameters(type)
  });
  
  const [showControls, setShowControls] = useState(false);
  const animationRef = useRef<number>();
  const canvasRef = useRef<HTMLDivElement>(null);

  // Initialize simulation based on type
  useEffect(() => {
    resetSimulation();
  }, [type]);

  // Animation loop
  useEffect(() => {
    if (simulation.isPlaying) {
      const animate = () => {
        setSimulation(prev => updateSimulation(prev, type));
        animationRef.current = requestAnimationFrame(animate);
      };
      animationRef.current = requestAnimationFrame(animate);
    }

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
    };
  }, [simulation.isPlaying, type]);

  const resetSimulation = () => {
    const objects = createInitialObjects(type, simulation.parameters);
    setSimulation(prev => ({
      ...prev,
      time: 0,
      objects,
      isPlaying: false
    }));
  };

  const togglePlay = () => {
    setSimulation(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const updateParameter = (parameter: string, value: number[]) => {
    const newValue = value[0];
    setSimulation(prev => ({
      ...prev,
      parameters: { ...prev.parameters, [parameter]: newValue }
    }));
    onParameterChange?.(parameter, newValue);
    
    // Reset simulation with new parameters
    setTimeout(resetSimulation, 100);
  };

  const renderSimulation = () => {
    switch (type) {
      case 'projectile':
        return <ProjectileMotion simulation={simulation} />;
      case 'pendulum':
        return <PendulumMotion simulation={simulation} />;
      case 'collision':
        return <CollisionSimulation simulation={simulation} />;
      case 'wave':
        return <WaveSimulation simulation={simulation} />;
      case 'spring':
        return <SpringMotion simulation={simulation} />;
      default:
        return <div>Simulation type not supported</div>;
    }
  };

  const renderControls = () => {
    const params = getParameterConfig(type, educationLevel);
    
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900">Simulation Controls</h4>
        {params.map((param) => (
          <div key={param.key} className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium text-gray-700">
                {param.label}
              </label>
              <span className="text-sm text-gray-500">
                {simulation.parameters[param.key]?.toFixed(1)} {param.unit}
              </span>
            </div>
            <Slider
              value={[simulation.parameters[param.key] || param.default]}
              onValueChange={(value) => updateParameter(param.key, value)}
              min={param.min}
              max={param.max}
              step={param.step}
              className="w-full"
            />
            <p className="text-xs text-gray-600">{param.description}</p>
          </div>
        ))}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg">{title}</CardTitle>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{type}</Badge>
            <Badge variant="outline">{educationLevel}</Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Simulation Display */}
        <div 
          ref={canvasRef}
          className="w-full h-80 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg relative overflow-hidden border-2 border-blue-200"
        >
          {renderSimulation()}
          
          {/* Play Controls Overlay */}
          <div className="absolute top-4 left-4 flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={togglePlay}
              className="bg-white/90 backdrop-blur-sm"
            >
              {simulation.isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={resetSimulation}
              className="bg-white/90 backdrop-blur-sm"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowControls(!showControls)}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {/* Time Display */}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
            <span className="text-sm font-medium">
              Time: {simulation.time.toFixed(1)}s
            </span>
          </div>
        </div>

        {/* Interactive Controls */}
        {showControls && renderControls()}

        {/* Educational Information */}
        <div className="bg-blue-50 rounded-lg p-4">
          <h4 className="font-medium text-blue-900 mb-2">Learning Objectives</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            {getLearningObjectives(type, educationLevel).map((objective, index) => (
              <li key={index} className="flex items-start space-x-2">
                <span className="text-blue-600 mt-1">•</span>
                <span>{objective}</span>
              </li>
            ))}
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

// Simulation Components
const ProjectileMotion: React.FC<{ simulation: SimulationState }> = ({ simulation }) => {
  const obj = simulation.objects[0];
  if (!obj) return null;

  return (
    <>
      {/* Trajectory Trail */}
      <svg className="absolute inset-0 pointer-events-none">
        <path
          d={`M ${obj.trail.map(p => `${p.x},${p.y}`).join(' L ')}`}
          stroke="rgba(59, 130, 246, 0.5)"
          strokeWidth="2"
          fill="none"
          strokeDasharray="5,5"
        />
      </svg>
      
      {/* Projectile */}
      <motion.div
        className="absolute w-4 h-4 bg-red-500 rounded-full shadow-lg"
        style={{ left: obj.x - 8, top: obj.y - 8 }}
        transition={{ duration: 0 }}
      />
      
      {/* Velocity Vector */}
      <svg className="absolute inset-0 pointer-events-none">
        <defs>
          <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
            <polygon points="0 0, 10 3.5, 0 7" fill="green" />
          </marker>
        </defs>
        <line
          x1={obj.x} y1={obj.y}
          x2={obj.x + obj.vx * 5} y2={obj.y + obj.vy * 5}
          stroke="green" strokeWidth="2" markerEnd="url(#arrowhead)"
        />
      </svg>
    </>
  );
};

const PendulumMotion: React.FC<{ simulation: SimulationState }> = ({ simulation }) => {
  const obj = simulation.objects[0];
  if (!obj) return null;

  const pivotX = 200;
  const pivotY = 50;

  return (
    <>
      {/* Pendulum String */}
      <svg className="absolute inset-0 pointer-events-none">
        <line
          x1={pivotX} y1={pivotY}
          x2={obj.x} y2={obj.y}
          stroke="gray" strokeWidth="2"
        />
        {/* Pivot Point */}
        <circle cx={pivotX} cy={pivotY} r="4" fill="black" />
      </svg>
      
      {/* Pendulum Bob */}
      <motion.div
        className="absolute w-6 h-6 bg-blue-500 rounded-full shadow-lg"
        style={{ left: obj.x - 12, top: obj.y - 12 }}
        transition={{ duration: 0 }}
      />
    </>
  );
};

const CollisionSimulation: React.FC<{ simulation: SimulationState }> = ({ simulation }) => {
  return (
    <>
      {simulation.objects.map((obj) => (
        <motion.div
          key={obj.id}
          className="absolute rounded-full shadow-lg"
          style={{
            left: obj.x - obj.radius,
            top: obj.y - obj.radius,
            width: obj.radius * 2,
            height: obj.radius * 2,
            backgroundColor: obj.color
          }}
          transition={{ duration: 0 }}
        />
      ))}
    </>
  );
};

const WaveSimulation: React.FC<{ simulation: SimulationState }> = ({ simulation }) => {
  const points = generateWavePoints(simulation.time, simulation.parameters);
  
  return (
    <svg className="absolute inset-0">
      <polyline
        points={points}
        fill="none"
        stroke="#10b981"
        strokeWidth="3"
      />
      {/* Reference Lines */}
      <line x1="0" y1="150" x2="400" y2="150" stroke="#6b7280" strokeWidth="1" strokeDasharray="5,5" />
    </svg>
  );
};

const SpringMotion: React.FC<{ simulation: SimulationState }> = ({ simulation }) => {
  const obj = simulation.objects[0];
  if (!obj) return null;

  return (
    <>
      {/* Spring */}
      <svg className="absolute inset-0 pointer-events-none">
        <path
          d={generateSpringPath(200, 100, obj.x, obj.y)}
          stroke="gray"
          strokeWidth="2"
          fill="none"
        />
      </svg>
      
      {/* Mass */}
      <motion.div
        className="absolute w-8 h-8 bg-orange-500 rounded shadow-lg"
        style={{ left: obj.x - 16, top: obj.y - 16 }}
        transition={{ duration: 0 }}
      />
    </>
  );
};

// Helper Functions
function getDefaultParameters(type: string): Record<string, number> {
  switch (type) {
    case 'projectile':
      return { velocity: 20, angle: 45, gravity: 9.8 };
    case 'pendulum':
      return { length: 100, gravity: 9.8, angle: 30 };
    case 'collision':
      return { mass1: 1, mass2: 1, velocity1: 5, velocity2: -3 };
    case 'wave':
      return { amplitude: 50, frequency: 1, wavelength: 100 };
    case 'spring':
      return { springConstant: 0.1, mass: 1, damping: 0.02 };
    default:
      return {};
  }
}

function createInitialObjects(type: string, parameters: Record<string, number>): PhysicsObject[] {
  switch (type) {
    case 'projectile':
      const angle = (parameters.angle || 45) * Math.PI / 180;
      const velocity = parameters.velocity || 20;
      return [{
        id: 'projectile',
        x: 50,
        y: 250,
        vx: velocity * Math.cos(angle),
        vy: -velocity * Math.sin(angle),
        mass: 1,
        radius: 8,
        color: '#ef4444',
        trail: []
      }];
    
    case 'pendulum':
      const length = parameters.length || 100;
      const startAngle = (parameters.angle || 30) * Math.PI / 180;
      return [{
        id: 'pendulum',
        x: 200 + length * Math.sin(startAngle),
        y: 50 + length * Math.cos(startAngle),
        vx: 0,
        vy: 0,
        mass: 1,
        radius: 12,
        color: '#3b82f6',
        trail: []
      }];
    
    default:
      return [];
  }
}

function updateSimulation(prev: SimulationState, type: string): SimulationState {
  const dt = 0.016; // 60 FPS
  const newTime = prev.time + dt;
  
  const newObjects = prev.objects.map(obj => {
    switch (type) {
      case 'projectile':
        const gravity = prev.parameters.gravity || 9.8;
        const newVy = obj.vy + gravity * dt * 10; // Scale for visualization
        const newX = obj.x + obj.vx * dt * 10;
        const newY = obj.y + obj.vy * dt * 10;
        
        // Add to trail
        const newTrail = [...obj.trail, { x: obj.x, y: obj.y }].slice(-50);
        
        return {
          ...obj,
          x: newX,
          y: newY,
          vy: newVy,
          trail: newTrail
        };
      
      default:
        return obj;
    }
  });

  return {
    ...prev,
    time: newTime,
    objects: newObjects
  };
}

function getParameterConfig(type: string, educationLevel: string) {
  // Return parameter configurations based on type and education level
  const configs = {
    projectile: [
      { key: 'velocity', label: 'Initial Velocity', min: 5, max: 50, step: 1, default: 20, unit: 'm/s', description: 'How fast the object is launched' },
      { key: 'angle', label: 'Launch Angle', min: 0, max: 90, step: 5, default: 45, unit: '°', description: 'Angle of launch from horizontal' },
      { key: 'gravity', label: 'Gravity', min: 1, max: 20, step: 0.5, default: 9.8, unit: 'm/s²', description: 'Gravitational acceleration' }
    ]
  };
  
  return configs[type] || [];
}

function getLearningObjectives(type: string, educationLevel: string): string[] {
  const objectives = {
    projectile: {
      elementary: ['Understand that objects fall down due to gravity', 'See how angle affects where objects land'],
      'middle-school': ['Learn about projectile motion', 'Understand velocity and acceleration', 'See the effect of gravity on motion'],
      'high-school': ['Analyze projectile motion components', 'Calculate range and maximum height', 'Understand vector components'],
      college: ['Apply kinematic equations', 'Analyze optimal launch angles', 'Consider air resistance effects']
    }
  };
  
  return objectives[type]?.[educationLevel] || ['Explore physics concepts through simulation'];
}

function generateWavePoints(time: number, parameters: Record<string, number>): string {
  const points = [];
  const amplitude = parameters.amplitude || 50;
  const frequency = parameters.frequency || 1;
  const wavelength = parameters.wavelength || 100;
  
  for (let x = 0; x <= 400; x += 2) {
    const y = 150 + amplitude * Math.sin((2 * Math.PI * x / wavelength) - (2 * Math.PI * frequency * time));
    points.push(`${x},${y}`);
  }
  
  return points.join(' ');
}

function generateSpringPath(x1: number, y1: number, x2: number, y2: number): string {
  // Generate a spring-like path between two points
  const coils = 8;
  const amplitude = 10;
  const length = Math.sqrt((x2 - x1) ** 2 + (y2 - y1) ** 2);
  
  let path = `M ${x1} ${y1}`;
  
  for (let i = 0; i <= coils * 4; i++) {
    const t = i / (coils * 4);
    const x = x1 + (x2 - x1) * t;
    const y = y1 + (y2 - y1) * t + amplitude * Math.sin(i * Math.PI / 2);
    path += ` L ${x} ${y}`;
  }
  
  return path;
}

export default PhysicsSimulationTemplate;
