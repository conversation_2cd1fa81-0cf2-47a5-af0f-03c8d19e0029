/**
 * Career Detail Modal Component
 * Displays comprehensive career information in a modal dialog
 */

import React from 'react';
import { CareerPath, CareerAnalysis } from '../types';
import { DIFFICULTY_LEVELS, EXPORT_FORMATS } from '../constants';
import { careerExportService } from '../services/career-export.service';
import { toast } from 'sonner';

import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  Briefcase, 
  Clock, 
  DollarSign, 
  TrendingUp,
  CheckCircle,
  Target,
  BookOpen,
  Award,
  Download,
  ExternalLink
} from "lucide-react";

interface CareerDetailModalProps {
  career: CareerPath | null;
  isOpen: boolean;
  onClose: () => void;
  onExport: (career: CareerPath, format: string) => void;
}

export function CareerDetailModal({ 
  career, 
  isOpen, 
  onClose, 
  onExport 
}: CareerDetailModalProps) {
  if (!career) return null;

  const getDifficultyColor = (difficulty: string) => {
    return DIFFICULTY_LEVELS[difficulty as keyof typeof DIFFICULTY_LEVELS]?.color || 'text-gray-600';
  };

  const handleExport = async (format: string) => {
    try {
      // Create a minimal career analysis for single career export
      const singleCareerAnalysis: CareerAnalysis = {
        careerPaths: [career],
        overallAnalysis: `Detailed analysis for ${career.jobTitle}`,
        recommendations: ['Review the career roadmap carefully', 'Start building required skills'],
        nextSteps: ['Create a learning plan', 'Network with professionals in this field'],
        generatedAt: new Date()
      };

      const exportOptions = {
        format: format as 'pdf' | 'docx' | 'json',
        includeRoadmaps: true,
        includeAnalysis: true,
        includeVisualization: false
      };

      switch (format) {
        case 'pdf':
          await careerExportService.exportToPDF(singleCareerAnalysis, exportOptions);
          break;
        case 'docx':
          await careerExportService.exportToWord(singleCareerAnalysis, exportOptions);
          break;
        case 'json':
          await careerExportService.exportToJSON(singleCareerAnalysis, exportOptions);
          break;
        default:
          throw new Error('Unsupported export format');
      }

      toast.success(`${career.jobTitle} exported as ${format.toUpperCase()}`);
    } catch (error) {
      console.error('Export failed:', error);
      toast.error('Failed to export career details');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] p-0">
        <DialogHeader className="p-6 pb-0">
          <DialogTitle className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Briefcase className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <h2 className="text-xl font-bold text-gray-900">{career.jobTitle}</h2>
                <p className="text-sm text-gray-600 mt-1">{career.jobDescription}</p>
              </div>
            </div>
          </DialogTitle>
        </DialogHeader>

        <ScrollArea className="flex-1 px-6">
          <div className="space-y-6 pb-6">
            {/* Key Metrics */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <Card>
                <CardContent className="p-4 text-center">
                  <Clock className="h-6 w-6 text-blue-500 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Timeline</p>
                  <p className="font-semibold text-gray-900">{career.timeline}</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <DollarSign className="h-6 w-6 text-green-500 mx-auto mb-2" />
                  <p className="text-sm text-gray-600">Salary Range</p>
                  <p className="font-semibold text-gray-900">{career.salary}</p>
                </CardContent>
              </Card>
              
              <Card>
                <CardContent className="p-4 text-center">
                  <TrendingUp className={`h-6 w-6 mx-auto mb-2 ${getDifficultyColor(career.difficulty)}`} />
                  <p className="text-sm text-gray-600">Difficulty</p>
                  <Badge className={getDifficultyColor(career.difficulty)}>
                    {career.difficulty}
                  </Badge>
                </CardContent>
              </Card>
              
              {career.workRequired && (
                <Card>
                  <CardContent className="p-4 text-center">
                    <Target className="h-6 w-6 text-purple-500 mx-auto mb-2" />
                    <p className="text-sm text-gray-600">Prep Time</p>
                    <p className="font-semibold text-gray-900">{career.workRequired}</p>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* About the Role */}
            {career.aboutTheRole && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <BookOpen className="h-5 w-5 mr-2 text-blue-600" />
                    About the Role
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-gray-700 leading-relaxed">{career.aboutTheRole}</p>
                </CardContent>
              </Card>
            )}

            {/* Why It's a Good Fit */}
            {career.whyItsGoodFit && career.whyItsGoodFit.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                    Why It's a Good Fit for You
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2">
                    {career.whyItsGoodFit.map((reason, index) => (
                      <li key={index} className="flex items-start">
                        <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                        <span className="text-gray-700">{reason}</span>
                      </li>
                    ))}
                  </ul>
                </CardContent>
              </Card>
            )}

            {/* Skills and Certifications */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {career.skills && career.skills.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Target className="h-5 w-5 mr-2 text-purple-600" />
                      Required Skills
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="flex flex-wrap gap-2">
                      {career.skills.map((skill, index) => (
                        <Badge key={index} variant="secondary">
                          {skill}
                        </Badge>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {career.certifications && career.certifications.length > 0 && (
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <Award className="h-5 w-5 mr-2 text-yellow-600" />
                      Recommended Certifications
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {career.certifications.map((cert, index) => (
                        <div key={index} className="flex items-center">
                          <Award className="h-4 w-4 text-yellow-500 mr-2" />
                          <span className="text-gray-700">{cert}</span>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </div>

            {/* Career Roadmap */}
            {career.roadmap && career.roadmap.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2 text-blue-600" />
                    Career Transition Roadmap
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {career.roadmap.map((step, index) => (
                      <div key={index} className="flex">
                        <div className="flex flex-col items-center mr-4">
                          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="text-sm font-semibold text-blue-600">{index + 1}</span>
                          </div>
                          {index < career.roadmap!.length - 1 && (
                            <div className="w-0.5 h-8 bg-blue-200 mt-2" />
                          )}
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">{step.timeframe}</h4>
                          <p className="text-gray-700 text-sm mb-2">{step.description}</p>
                          {step.tasks && step.tasks.length > 0 && (
                            <ul className="text-sm text-gray-600 space-y-1">
                              {step.tasks.map((task, taskIndex) => (
                                <li key={taskIndex} className="flex items-start">
                                  <span className="w-1 h-1 bg-gray-400 rounded-full mt-2 mr-2 flex-shrink-0" />
                                  {task}
                                </li>
                              ))}
                            </ul>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </ScrollArea>

        <Separator />

        <DialogFooter className="p-6 pt-4">
          <div className="flex items-center justify-between w-full">
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">Export this career plan:</span>
              {EXPORT_FORMATS.map((format) => (
                <Button
                  key={format.id}
                  variant="outline"
                  size="sm"
                  onClick={() => handleExport(format.id)}
                  className="text-xs"
                >
                  <Download className="h-3 w-3 mr-1" />
                  {format.name}
                </Button>
              ))}
            </div>
            
            <div className="flex items-center space-x-2">
              <Button variant="outline" onClick={onClose}>
                Close
              </Button>
              <Button onClick={() => window.open(`https://www.google.com/search?q=${encodeURIComponent(career.jobTitle + ' career guide')}`, '_blank')}>
                <ExternalLink className="h-4 w-4 mr-2" />
                Learn More
              </Button>
            </div>
          </div>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
