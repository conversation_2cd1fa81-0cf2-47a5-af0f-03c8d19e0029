/**
 * Career Explorer - AI-Powered Career Discovery Platform
 * Main component for the career exploration module
 */

import React, { useState, useEffect } from 'react';
import { toast } from 'sonner';
import { 
  CareerExplorerProps,
  CareerPath,
  ResumeData,
  CareerAnalysis
} from './types';
import { useCareerExplorerStore } from './stores/career-explorer.store';
import { careerAIService } from './services/career-ai.service';
import { pdfParserService } from './services/pdf-parser.service';
import { useAuth } from '@/contexts/AuthContext';

// Component imports (to be created)
import { ResumeUploadForm } from './components/ResumeUploadForm';
import { CareerVisualization } from './components/CareerVisualization';
import { CareerDetailModal } from './components/CareerDetailModal';
import { CareerExportDialog } from './components/CareerExportDialog';
import { CareerHistoryPanel } from './components/CareerHistoryPanel';

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { 
  ArrowLeft, 
  ArrowRight, 
  Briefcase, 
  Download, 
  History, 
  RefreshCw,
  Sparkles,
  Target,
  TrendingUp
} from "lucide-react";

export function CareerExplorer({ className }: CareerExplorerProps) {
  const { user } = useAuth();
  
  // Store state
  const {
    currentStep,
    resumeData,
    additionalContext,
    selectedModel,
    careerAnalysis,
    isGenerating,
    error,
    setResumeData,
    setAdditionalContext,
    setSelectedModel,
    setCurrentStep,
    setCareerAnalysis,
    setIsGenerating,
    setError,
    nextStep,
    previousStep,
    resetState,
    clearError
  } = useCareerExplorerStore();

  // Local state
  const [selectedCareer, setSelectedCareer] = useState<CareerPath | null>(null);
  const [showExportDialog, setShowExportDialog] = useState(false);
  const [showHistoryPanel, setShowHistoryPanel] = useState(false);
  const [generationProgress, setGenerationProgress] = useState(0);

  // Initialize component
  useEffect(() => {
    if (!user) {
      setError('Please sign in to use the Career Explorer');
    }
  }, [user, setError]);

  // Handle resume upload
  const handleResumeUpload = async (file: File) => {
    try {
      setIsGenerating(true);
      clearError();

      // Validate file
      const validation = pdfParserService.validateFile(file);
      if (!validation.isValid) {
        throw new Error(validation.error);
      }

      // Parse file
      const resumeData = await pdfParserService.parseFile(file);
      setResumeData(resumeData);
      
      toast.success('Resume uploaded and parsed successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to upload resume';
      setError(errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  // Handle career generation
  const handleGenerateCareers = async () => {
    if (!resumeData) {
      setError('Please upload a resume first');
      return;
    }

    try {
      setIsGenerating(true);
      setCurrentStep('processing');
      setGenerationProgress(0);
      clearError();

      // Generate initial career paths
      setGenerationProgress(25);
      const response = await careerAIService.generateCareerPaths({
        resumeText: resumeData.rawText,
        additionalContext,
        model: selectedModel
      });

      setGenerationProgress(50);

      // Generate detailed analysis for each career
      const detailedCareers = await careerAIService.generateAllDetailedAnalyses(
        response.careers,
        resumeData,
        additionalContext,
        selectedModel,
        (completed, total) => {
          const progress = 50 + (completed / total) * 50;
          setGenerationProgress(progress);
        }
      );

      // Create career analysis object
      const analysis: CareerAnalysis = {
        careerPaths: detailedCareers,
        overallAnalysis: response.analysis,
        recommendations: [
          'Review each career path carefully',
          'Consider your personal interests and goals',
          'Start with the career that has the lowest difficulty rating',
          'Focus on building the required skills gradually'
        ],
        nextSteps: [
          'Choose 1-2 careers that interest you most',
          'Create a detailed learning plan',
          'Start building relevant skills',
          'Network with professionals in your target field'
        ],
        generatedAt: new Date()
      };

      setCareerAnalysis(analysis);
      setCurrentStep('visualization');
      setGenerationProgress(100);
      
      toast.success('Career analysis completed successfully!');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to generate career analysis';
      setError(errorMessage);
      toast.error(errorMessage);
      setCurrentStep('input');
    } finally {
      setIsGenerating(false);
      setGenerationProgress(0);
    }
  };

  // Handle career selection
  const handleCareerSelect = (career: CareerPath) => {
    setSelectedCareer(career);
  };

  // Handle export
  const handleExport = () => {
    if (!careerAnalysis) {
      toast.error('No career analysis to export');
      return;
    }
    setShowExportDialog(true);
  };

  // Handle reset
  const handleReset = () => {
    resetState();
    setSelectedCareer(null);
    setShowExportDialog(false);
    setShowHistoryPanel(false);
    toast.success('Career explorer reset');
  };

  // Render step indicator
  const renderStepIndicator = () => {
    const steps = [
      { id: 'input', label: 'Upload Resume', icon: Briefcase },
      { id: 'processing', label: 'AI Analysis', icon: Sparkles },
      { id: 'visualization', label: 'Explore Careers', icon: Target },
      { id: 'export', label: 'Export Results', icon: Download }
    ];

    return (
      <div className="flex items-center justify-center mb-8">
        {steps.map((step, index) => {
          const isActive = currentStep === step.id;
          const isCompleted = steps.findIndex(s => s.id === currentStep) > index;
          const StepIcon = step.icon;

          return (
            <div key={step.id} className="flex items-center">
              <div className={`flex items-center justify-center w-10 h-10 rounded-full border-2 ${
                isActive 
                  ? 'border-blue-500 bg-blue-500 text-white' 
                  : isCompleted 
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-gray-300 bg-white text-gray-400'
              }`}>
                <StepIcon className="w-5 h-5" />
              </div>
              <span className={`ml-2 text-sm font-medium ${
                isActive ? 'text-blue-600' : isCompleted ? 'text-green-600' : 'text-gray-400'
              }`}>
                {step.label}
              </span>
              {index < steps.length - 1 && (
                <div className={`w-12 h-0.5 mx-4 ${
                  isCompleted ? 'bg-green-500' : 'bg-gray-300'
                }`} />
              )}
            </div>
          );
        })}
      </div>
    );
  };

  // Show authentication prompt if not authenticated
  if (!user) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
        <Card className="w-96">
          <CardContent className="p-6 text-center">
            <Briefcase className="h-12 w-12 text-blue-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Authentication Required</h3>
            <p className="text-gray-600 mb-4">
              Please sign in to access the Career Explorer and discover your ideal career paths.
            </p>
            <Button
              onClick={() => window.location.href = '/auth'}
              className="w-full"
            >
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 ${className}`}>
      {/* Header */}
      <div className="bg-white border-b shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <TrendingUp className="h-6 w-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Career Explorer</h1>
                <p className="text-sm text-gray-600">Discover your ideal career paths with AI</p>
              </div>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowHistoryPanel(true)}
              >
                <History className="h-4 w-4 mr-2" />
                History
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleReset}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Reset
              </Button>
              {careerAnalysis && (
                <Button
                  size="sm"
                  onClick={handleExport}
                >
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {renderStepIndicator()}

        {/* Error Alert */}
        {error && (
          <Alert className="mb-6 border-red-200 bg-red-50">
            <AlertDescription className="text-red-800">
              {error}
            </AlertDescription>
          </Alert>
        )}

        {/* Processing Progress */}
        {isGenerating && currentStep === 'processing' && (
          <Card className="mb-6">
            <CardContent className="p-6">
              <div className="text-center">
                <Sparkles className="h-8 w-8 text-blue-500 mx-auto mb-4 animate-spin" />
                <h3 className="text-lg font-semibold mb-2">Analyzing Your Career Potential</h3>
                <p className="text-gray-600 mb-4">
                  Our AI is analyzing your resume and generating personalized career recommendations...
                </p>
                <Progress value={generationProgress} className="w-full" />
                <p className="text-sm text-gray-500 mt-2">{generationProgress}% complete</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Step Content */}
        {currentStep === 'input' && (
          <ResumeUploadForm
            onResumeUpload={handleResumeUpload}
            onContextChange={setAdditionalContext}
            onModelChange={setSelectedModel}
            onGenerate={handleGenerateCareers}
            isLoading={isGenerating}
            error={error}
            resumeData={resumeData}
            additionalContext={additionalContext}
            selectedModel={selectedModel}
          />
        )}

        {currentStep === 'visualization' && careerAnalysis && (
          <CareerVisualization
            careerPaths={careerAnalysis.careerPaths}
            onCareerSelect={handleCareerSelect}
          />
        )}

        {/* Navigation */}
        {currentStep !== 'input' && currentStep !== 'processing' && (
          <div className="flex justify-between mt-8">
            <Button
              variant="outline"
              onClick={previousStep}
              disabled={currentStep === 'input'}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
            <Button
              onClick={nextStep}
              disabled={currentStep === 'export'}
            >
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          </div>
        )}
      </div>

      {/* Modals */}
      <CareerDetailModal
        career={selectedCareer}
        isOpen={!!selectedCareer}
        onClose={() => setSelectedCareer(null)}
        onExport={(career, format) => {
          // Handle individual career export
          toast.success(`Exporting ${career.jobTitle} as ${format}`);
        }}
      />

      <CareerExportDialog
        isOpen={showExportDialog}
        onClose={() => setShowExportDialog(false)}
        careerAnalysis={careerAnalysis}
      />

      <CareerHistoryPanel
        isOpen={showHistoryPanel}
        onClose={() => setShowHistoryPanel(false)}
        onLoadExploration={(exploration) => {
          // Handle loading previous exploration
          toast.success('Loading previous exploration...');
          setShowHistoryPanel(false);
        }}
        onCreateNew={() => {
          handleReset();
          setShowHistoryPanel(false);
        }}
      />
    </div>
  );
}
