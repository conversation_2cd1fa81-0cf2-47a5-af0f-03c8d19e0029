import { LucideIcon } from "lucide-react";
import { ComponentType } from "react";

// Core tutoring types
export interface TutorSession {
  id: string;
  title: string;
  topic: string;
  educationLevel: EducationLevel;
  messages: TutorMessage[];
  sources: TutorSource[];
  createdAt: Date;
  updatedAt: Date;
  userId?: string;
  isActive: boolean;
  metadata?: {
    totalMessages: number;
    duration: number; // in minutes
    concepts: string[];
    difficulty: 'beginner' | 'intermediate' | 'advanced';
  };
}

export interface TutorMessage {
  id: string;
  role: 'user' | 'assistant' | 'system';
  content: string;
  timestamp: Date;
  sources?: string[]; // IDs of sources referenced in this message
  isStreaming?: boolean;
  metadata?: {
    tokens?: number;
    model?: string;
    confidence?: number;
  };
}

export interface TutorSource {
  id: string;
  title: string;
  url: string;
  snippet: string;
  domain: string;
  publishedDate?: string;
  score: number;
  type: 'academic' | 'educational' | 'web' | 'video' | 'book';
  relevanceScore: number;
  educationLevel?: EducationLevel;
}

// Education levels
export type EducationLevel = 
  | 'elementary'
  | 'middle-school'
  | 'high-school'
  | 'college'
  | 'undergraduate'
  | 'graduate'
  | 'professional';

export interface EducationLevelOption {
  id: EducationLevel;
  name: string;
  description: string;
  icon: LucideIcon;
  ageRange: string;
  complexity: number; // 1-10 scale
}

// AI model configuration
export interface TutorAIModel {
  id: string;
  name: string;
  provider: string;
  apiProvider: 'gemini' | 'openrouter'; // Which API service to use
  description: string;
  maxTokens: number;
  supportsStreaming: boolean;
  cost: 'low' | 'medium' | 'high';
  strengths: string[];
  bestFor: string[];
  educationLevels: EducationLevel[];
}

// Tutoring settings
export interface TutorSettings {
  preferredModel: string;
  educationLevel: EducationLevel;
  learningStyle: 'visual' | 'auditory' | 'kinesthetic' | 'reading';
  pace: 'slow' | 'normal' | 'fast';
  includeExamples: boolean;
  includeQuizzes: boolean;
  sourcesEnabled: boolean;
  maxSources: number;
  language: string;
  notifications: {
    sessionReminders: boolean;
    progressUpdates: boolean;
  };
}

// Topic suggestions
export interface TopicSuggestion {
  id: string;
  name: string;
  category: string;
  icon: string;
  description: string;
  difficulty: 'beginner' | 'intermediate' | 'advanced';
  estimatedTime: string; // e.g., "30 minutes"
  prerequisites?: string[];
  relatedTopics: string[];
}

// Learning progress
export interface LearningProgress {
  sessionId: string;
  topic: string;
  conceptsLearned: string[];
  questionsAsked: number;
  timeSpent: number; // in minutes
  comprehensionLevel: number; // 1-10 scale
  nextSteps: string[];
  achievements: string[];
}

// Export options
export interface TutorExportOptions {
  format: 'pdf' | 'docx' | 'txt' | 'html';
  includeMessages: boolean;
  includeSources: boolean;
  includeProgress: boolean;
  includeMetadata: boolean;
  title?: string;
  author?: string;
}

// Search and source options
export interface TutorSearchOptions {
  maxResults: number;
  searchDepth: 'basic' | 'comprehensive' | 'advanced';
  includeImages: boolean;
  includeVideos: boolean;
  educationLevel: EducationLevel;
  sourceTypes: ('academic' | 'educational' | 'web' | 'video' | 'book')[];
  timeFilter?: 'day' | 'week' | 'month' | 'year' | 'all';
}

// API response types
export interface TutorResponse {
  content: string;
  sources: TutorSource[];
  confidence: number;
  tokens: number;
  model: string;
  suggestions?: string[];
}

export interface StreamingTutorResponse {
  chunk: string;
  isComplete: boolean;
  sources?: TutorSource[];
  metadata?: {
    tokens: number;
    model: string;
  };
}

// Database types for Supabase integration
export interface TutorSessionDB {
  id: string;
  user_id: string;
  title: string;
  topic: string;
  education_level: EducationLevel;
  messages: TutorMessage[];
  sources: TutorSource[];
  created_at: string;
  updated_at: string;
  is_active: boolean;
  metadata?: any;
}

export interface TutorProgressDB {
  id: string;
  user_id: string;
  session_id: string;
  concepts_learned: string[];
  questions_asked: number;
  time_spent: number;
  comprehension_level: number;
  created_at: string;
  updated_at: string;
}

// Component props
export interface TutorHeroProps {
  onStartSession: (topic: string, educationLevel: EducationLevel) => void;
  isLoading: boolean;
  suggestions: TopicSuggestion[];
}

export interface TutorChatProps {
  session: TutorSession;
  onSendMessage: (message: string) => void;
  onEndSession: () => void;
  isLoading: boolean;
  isStreaming: boolean;
}

export interface TutorSourcesProps {
  sources: TutorSource[];
  isLoading: boolean;
  onSourceClick: (source: TutorSource) => void;
}

export interface TutorHistoryProps {
  sessions: TutorSession[];
  onSessionSelect: (session: TutorSession) => void;
  onSessionDelete: (sessionId: string) => void;
  onCreateNew: () => void;
}

// Educational Visualization Types
export type Subject =
  | 'physics'
  | 'mathematics'
  | 'chemistry'
  | 'biology'
  | 'computer-science'
  | 'engineering'
  | 'economics'
  | 'general';

export type VisualizationType =
  | 'animation'
  | '3d-model'
  | 'interactive-chart'
  | 'simulation'
  | 'diagram'
  | 'graph'
  | 'timeline';

export type GradeLevel = 'elementary' | 'middle' | 'high' | 'college';
export type DifficultyLevel = 'beginner' | 'intermediate' | 'advanced';

export interface VisualizationParameters {
  [key: string]: number | string | boolean | number[] | string[];
}

export interface AnimationControls {
  isPlaying: boolean;
  speed: number; // 0.1 to 3.0
  currentStep: number;
  totalSteps: number;
  canReset: boolean;
  canStepThrough: boolean;
  loop: boolean;
  autoPlay: boolean;
}

export interface EducationalVisualization {
  id: string;
  subject: Subject;
  topic: string;
  title: string;
  description: string;
  gradeLevel: GradeLevel;
  difficulty: DifficultyLevel;
  visualizationType: VisualizationType;
  parameters: VisualizationParameters;
  learningObjectives: string[];
  keyPoints: string[];
  interactiveElements: string[];
  estimatedDuration: number; // in minutes
  prerequisites?: string[];
  relatedConcepts: string[];
  createdAt: Date;
  updatedAt: Date;
}

export interface EducationalVisualizationProps {
  topic: string;
  gradeLevel: GradeLevel;
  difficulty?: DifficultyLevel;
  interactive?: boolean;
  subject: Subject;
  parameters?: VisualizationParameters;
  onParameterChange?: (key: string, value: any) => void;
  onComplete?: () => void;
  className?: string;
}

export interface VisualizationDetectionResult {
  shouldVisualize: boolean;
  subject: Subject;
  topic: string;
  suggestedVisualization: string;
  confidence: number;
  gradeAppropriate: boolean;
  visualizationType: VisualizationType;
  estimatedComplexity: DifficultyLevel;
  reasoning: string;
}

export interface SubjectVisualization {
  id: string;
  subject: Subject;
  topic: string;
  title: string;
  description: string;
  gradeLevel: GradeLevel;
  difficulty: DifficultyLevel;
  component: ComponentType<EducationalVisualizationProps>;
  parameters: VisualizationParameters;
  learningObjectives: string[];
  tags: string[];
  popularity: number;
  effectiveness: number; // 1-10 scale
}

export interface VisualizationState {
  currentVisualization: EducationalVisualization | null;
  animationControls: AnimationControls;
  gradeLevel: GradeLevel;
  preferredComplexity: DifficultyLevel;
  visualizationHistory: VisualizationHistoryItem[];
  isVisualizationEnabled: boolean;
  autoDetectVisualizations: boolean;
  preferredSubjects: Subject[];
  visualizationPreferences: {
    autoPlay: boolean;
    showControls: boolean;
    showExplanations: boolean;
    enableInteractivity: boolean;
    preferredSpeed: number;
  };
}

export interface VisualizationHistoryItem {
  id: string;
  visualization: EducationalVisualization;
  sessionId: string;
  timestamp: Date;
  timeSpent: number; // in seconds
  interactionCount: number;
  completed: boolean;
  userRating?: number; // 1-5 scale
  feedback?: string;
}

// Enhanced Tutor Message with Visualization Support
export interface EnhancedTutorMessage extends TutorMessage {
  visualization?: EducationalVisualization;
  hasVisualization: boolean;
  visualizationDetection?: VisualizationDetectionResult;
}

// Store state interface
export interface TutorStoreState {
  // Current session
  currentSession: TutorSession | null;
  isSessionActive: boolean;

  // UI state
  currentView: 'hero' | 'chat' | 'history' | 'settings';
  isLoading: boolean;
  isStreaming: boolean;
  error: string | null;

  // Settings
  settings: TutorSettings;

  // History
  sessions: TutorSession[];

  // Sources
  isLoadingSources: boolean;

  // Visualization state
  visualizationState: VisualizationState;
  availableVisualizations: SubjectVisualization[];
  isGeneratingVisualization: boolean;

  // Actions
  startSession: (topic: string, educationLevel: EducationLevel) => Promise<void>;
  sendMessage: (message: string) => Promise<void>;
  endSession: () => void;
  loadSession: (sessionId: string) => Promise<void>;
  deleteSession: (sessionId: string) => Promise<void>;
  updateSettings: (settings: Partial<TutorSettings>) => void;
  setCurrentView: (view: 'hero' | 'chat' | 'history' | 'settings') => void;
  clearError: () => void;

  // Visualization actions
  detectVisualization: (message: string, context: string) => Promise<VisualizationDetectionResult>;
  generateVisualization: (detection: VisualizationDetectionResult) => Promise<EducationalVisualization>;
  updateAnimationControls: (controls: Partial<AnimationControls>) => void;
  updateVisualizationPreferences: (preferences: Partial<VisualizationState['visualizationPreferences']>) => void;
  addVisualizationToHistory: (item: VisualizationHistoryItem) => void;
  setCurrentVisualization: (visualization: EducationalVisualization | null) => void;
}

// Subject-Specific Visualization Types

// Physics Visualizations
export interface PhysicsVisualizationProps extends EducationalVisualizationProps {
  physicsType: 'motion' | 'waves' | 'forces' | 'electricity' | 'quantum' | 'thermodynamics';
  showVectors?: boolean;
  showTrajectory?: boolean;
  showForceLines?: boolean;
  enableGravity?: boolean;
  enableFriction?: boolean;
}

// Mathematics Visualizations
export interface MathVisualizationProps extends EducationalVisualizationProps {
  mathType: 'calculus' | 'algebra' | 'geometry' | 'statistics' | 'trigonometry' | 'linear-algebra';
  showGrid?: boolean;
  showAxes?: boolean;
  showDerivative?: boolean;
  showIntegral?: boolean;
  animateTransformation?: boolean;
}

// Chemistry Visualizations
export interface ChemistryVisualizationProps extends EducationalVisualizationProps {
  chemistryType: 'molecular-structure' | 'reactions' | 'bonding' | 'periodic-trends';
  show3D?: boolean;
  showElectrons?: boolean;
  showBonds?: boolean;
  enableRotation?: boolean;
  showEnergyLevels?: boolean;
}

// Biology Visualizations
export interface BiologyVisualizationProps extends EducationalVisualizationProps {
  biologyType: 'cell-processes' | 'genetics' | 'evolution' | 'anatomy' | 'ecosystems';
  showCellStructure?: boolean;
  showDNA?: boolean;
  showProteinSynthesis?: boolean;
  enableZoom?: boolean;
  showTimeProgression?: boolean;
}

// Computer Science Visualizations
export interface CSVisualizationProps extends EducationalVisualizationProps {
  csType: 'algorithms' | 'data-structures' | 'networking' | 'machine-learning';
  showCode?: boolean;
  showStepByStep?: boolean;
  showComplexity?: boolean;
  enableInteraction?: boolean;
  showDataFlow?: boolean;
}

// Visualization Export Types
export interface VisualizationExportOptions {
  format: 'png' | 'svg' | 'gif' | 'mp4' | 'html';
  quality: 'low' | 'medium' | 'high';
  includeControls: boolean;
  includeExplanation: boolean;
  duration?: number; // for video exports
  fps?: number; // for video exports
  width?: number;
  height?: number;
}

// Animation Framework Types
export interface AnimationFrame {
  id: string;
  timestamp: number;
  description: string;
  parameters: VisualizationParameters;
  duration: number; // in milliseconds
  easing?: 'linear' | 'ease-in' | 'ease-out' | 'ease-in-out' | 'bounce';
}

export interface AnimationSequence {
  id: string;
  name: string;
  frames: AnimationFrame[];
  totalDuration: number;
  loop: boolean;
  autoPlay: boolean;
}

// Grade Level Adaptation Types
export interface GradeLevelContent {
  gradeLevel: GradeLevel;
  vocabulary: string[];
  concepts: string[];
  complexity: number; // 1-10 scale
  explanationStyle: 'simple' | 'detailed' | 'technical';
  visualComplexity: 'basic' | 'intermediate' | 'advanced';
  interactivityLevel: 'guided' | 'semi-guided' | 'free-exploration';
}

// Learning Analytics Types
export interface VisualizationAnalytics {
  visualizationId: string;
  userId: string;
  sessionId: string;
  startTime: Date;
  endTime?: Date;
  totalTimeSpent: number; // in seconds
  interactionCount: number;
  pauseCount: number;
  replayCount: number;
  speedChanges: number;
  parametersChanged: string[];
  completionRate: number; // 0-1
  engagementScore: number; // 1-10
  learningEffectiveness: number; // 1-10
}

// Error Handling Types
export interface VisualizationError {
  code: string;
  message: string;
  severity: 'low' | 'medium' | 'high';
  context: {
    visualizationId?: string;
    subject?: Subject;
    gradeLevel?: GradeLevel;
    userAction?: string;
  };
  timestamp: Date;
  resolved: boolean;
}
