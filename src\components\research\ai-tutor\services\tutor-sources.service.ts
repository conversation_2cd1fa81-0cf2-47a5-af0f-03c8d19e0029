/**
 * Tutor Sources Service
 * Handles fetching and processing educational sources for tutoring sessions
 * Integrates with existing search infrastructure (Tavily, Google Search)
 */

import { TutorSource, TutorSearchOptions, EducationLevel } from '../types';
import { tavilySearchService } from '../../research-search/services/tavily-search.service';
import { TavilySearchResult } from '../../research-search/types';

interface SourceSearchResult {
  sources: TutorSource[];
  totalFound: number;
  searchTime: number;
}

class TutorSourcesService {
  
  /**
   * Search for educational sources on a given topic
   */
  async searchEducationalSources(
    topic: string,
    options: Partial<TutorSearchOptions> = {}
  ): Promise<SourceSearchResult> {
    const startTime = Date.now();
    
    const searchOptions: TutorSearchOptions = {
      maxResults: options.maxResults || 8,
      searchDepth: options.searchDepth || 'comprehensive',
      includeImages: options.includeImages || false,
      includeVideos: options.includeVideos || true,
      educationLevel: options.educationLevel || 'high-school',
      sourceTypes: options.sourceTypes || ['academic', 'educational', 'web'],
      timeFilter: options.timeFilter || 'all'
    };

    try {
      // Check if Tavily service is available
      if (!tavilySearchService.isConfigured()) {
        console.warn('Tavily search not configured, returning empty sources');
        return {
          sources: [],
          totalFound: 0,
          searchTime: Date.now() - startTime
        };
      }

      // Enhance query for educational content
      const enhancedQuery = this.enhanceQueryForEducation(topic, searchOptions.educationLevel);

      // Search using Tavily for academic and educational sources
      const tavilyResults = await tavilySearchService.searchAcademic(enhancedQuery, {
        maxResults: searchOptions.maxResults * 2, // Get more results for filtering
        searchDepth: searchOptions.searchDepth,
        includeImages: searchOptions.includeImages,
        includeAnswer: true
      });

      // Transform and filter results
      const sources = this.transformAndFilterSources(tavilyResults, searchOptions);

      return {
        sources: sources.slice(0, searchOptions.maxResults),
        totalFound: tavilyResults.results?.length || 0,
        searchTime: Date.now() - startTime
      };

    } catch (error) {
      console.error('Error searching educational sources:', error);
      return {
        sources: [],
        totalFound: 0,
        searchTime: Date.now() - startTime
      };
    }
  }

  /**
   * Get sources for a specific learning topic with education level optimization
   */
  async getTopicSources(
    topic: string,
    educationLevel: EducationLevel,
    maxSources: number = 5
  ): Promise<TutorSource[]> {
    const result = await this.searchEducationalSources(topic, {
      maxResults: maxSources,
      educationLevel,
      searchDepth: 'comprehensive',
      sourceTypes: ['academic', 'educational', 'web']
    });

    return result.sources;
  }

  /**
   * Enhance search query for educational content
   */
  private enhanceQueryForEducation(topic: string, educationLevel: EducationLevel): string {
    const levelKeywords = this.getEducationLevelKeywords(educationLevel);
    const educationalKeywords = [
      'learn', 'tutorial', 'guide', 'explanation', 'basics', 'introduction',
      'course', 'lesson', 'study', 'education', 'teaching'
    ];

    // Combine topic with educational and level-specific keywords
    const enhancedTerms = [
      topic,
      ...levelKeywords.slice(0, 2), // Add top 2 level keywords
      ...educationalKeywords.slice(0, 3) // Add top 3 educational keywords
    ];

    return enhancedTerms.join(' ');
  }

  /**
   * Get keywords specific to education level
   */
  private getEducationLevelKeywords(educationLevel: EducationLevel): string[] {
    const keywordMap: Record<EducationLevel, string[]> = {
      'elementary': ['kids', 'children', 'simple', 'basic', 'elementary', 'primary'],
      'middle-school': ['middle school', 'junior high', 'teens', 'intermediate', 'grade 6-8'],
      'high-school': ['high school', 'secondary', 'teenagers', 'grade 9-12', 'advanced'],
      'college': ['college', 'university', 'undergraduate', 'higher education', 'academic'],
      'undergraduate': ['undergraduate', 'bachelor', 'university', 'college level', 'degree'],
      'graduate': ['graduate', 'masters', 'PhD', 'research', 'advanced study', 'postgraduate'],
      'professional': ['professional', 'workplace', 'career', 'industry', 'practical', 'applied']
    };

    return keywordMap[educationLevel] || [];
  }

  /**
   * Transform Tavily results to TutorSource format and apply filtering
   */
  private transformAndFilterSources(
    tavilyResults: TavilySearchResult,
    options: TutorSearchOptions
  ): TutorSource[] {
    if (!tavilyResults.results) {
      return [];
    }

    return tavilyResults.results
      .map((result, index) => this.transformToTutorSource(result, index, options))
      .filter(source => this.isSourceAppropriate(source, options))
      .sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Transform a single Tavily result to TutorSource
   */
  private transformToTutorSource(
    result: any,
    index: number,
    options: TutorSearchOptions
  ): TutorSource {
    const domain = this.extractDomain(result.url);
    const sourceType = this.determineSourceType(result.url, result.title);
    const relevanceScore = this.calculateRelevanceScore(result, options);

    return {
      id: `tutor-source-${index}`,
      title: result.title || 'Untitled',
      url: result.url,
      snippet: this.cleanSnippet(result.content || ''),
      domain,
      publishedDate: result.published_date,
      score: result.score || 0.5,
      type: sourceType,
      relevanceScore,
      educationLevel: this.inferEducationLevel(result.title, result.content, domain)
    };
  }

  /**
   * Determine if a source is appropriate for the given options
   */
  private isSourceAppropriate(source: TutorSource, options: TutorSearchOptions): boolean {
    // Check if source type is in allowed types
    if (!options.sourceTypes.includes(source.type)) {
      return false;
    }

    // Filter out inappropriate domains
    const inappropriateDomains = [
      'reddit.com', 'quora.com', 'yahoo.com', 'answers.com',
      'wikihow.com', 'ehow.com', 'ask.com'
    ];

    if (inappropriateDomains.some(domain => source.domain.includes(domain))) {
      return false;
    }

    // Ensure minimum relevance score
    if (source.relevanceScore < 0.3) {
      return false;
    }

    return true;
  }

  /**
   * Calculate relevance score for educational content
   */
  private calculateRelevanceScore(result: any, options: TutorSearchOptions): number {
    let score = result.score || 0.5;

    // Boost educational domains
    const educationalDomains = [
      'edu', 'khanacademy.org', 'coursera.org', 'edx.org', 'mit.edu',
      'stanford.edu', 'harvard.edu', 'wikipedia.org', 'britannica.com',
      'nationalgeographic.com', 'smithsonianmag.com', 'scientificamerican.com'
    ];

    if (educationalDomains.some(domain => result.url.includes(domain))) {
      score += 0.2;
    }

    // Boost based on educational keywords in title
    const educationalKeywords = ['learn', 'tutorial', 'guide', 'course', 'lesson', 'study'];
    const titleLower = (result.title || '').toLowerCase();
    const keywordMatches = educationalKeywords.filter(keyword => titleLower.includes(keyword));
    score += keywordMatches.length * 0.1;

    // Boost based on education level match
    const levelKeywords = this.getEducationLevelKeywords(options.educationLevel);
    const levelMatches = levelKeywords.filter(keyword => 
      titleLower.includes(keyword) || (result.content || '').toLowerCase().includes(keyword)
    );
    score += levelMatches.length * 0.05;

    return Math.min(score, 1.0);
  }

  /**
   * Determine source type based on URL and content
   */
  private determineSourceType(url: string, title: string): TutorSource['type'] {
    const domain = this.extractDomain(url);
    const titleLower = title.toLowerCase();

    // Academic sources
    if (domain.includes('.edu') || domain.includes('scholar.google') || 
        domain.includes('arxiv.org') || domain.includes('jstor.org')) {
      return 'academic';
    }

    // Educational platforms
    if (domain.includes('khanacademy') || domain.includes('coursera') || 
        domain.includes('edx') || domain.includes('udemy') ||
        titleLower.includes('course') || titleLower.includes('lesson')) {
      return 'educational';
    }

    // Video sources
    if (domain.includes('youtube') || domain.includes('vimeo') || 
        titleLower.includes('video') || titleLower.includes('watch')) {
      return 'video';
    }

    // Books and publications
    if (domain.includes('books.google') || domain.includes('amazon') ||
        titleLower.includes('book') || titleLower.includes('textbook')) {
      return 'book';
    }

    return 'web';
  }

  /**
   * Infer education level from content
   */
  private inferEducationLevel(title: string, content: string, domain: string): EducationLevel | undefined {
    const text = `${title} ${content}`.toLowerCase();

    if (text.includes('elementary') || text.includes('kids') || text.includes('children')) {
      return 'elementary';
    }
    if (text.includes('middle school') || text.includes('junior high')) {
      return 'middle-school';
    }
    if (text.includes('high school') || text.includes('secondary')) {
      return 'high-school';
    }
    if (text.includes('college') || text.includes('university') || domain.includes('.edu')) {
      return 'college';
    }
    if (text.includes('graduate') || text.includes('phd') || text.includes('masters')) {
      return 'graduate';
    }

    return undefined;
  }

  /**
   * Extract domain from URL
   */
  private extractDomain(url: string): string {
    try {
      return new URL(url).hostname.replace('www.', '');
    } catch {
      return 'unknown';
    }
  }

  /**
   * Clean and truncate snippet text
   */
  private cleanSnippet(content: string): string {
    // Remove extra whitespace and truncate
    const cleaned = content.replace(/\s+/g, ' ').trim();
    return cleaned.length > 200 ? cleaned.substring(0, 200) + '...' : cleaned;
  }

  /**
   * Check if the service is configured
   */
  isConfigured(): boolean {
    return tavilySearchService.isConfigured();
  }
}

export const tutorSourcesService = new TutorSourcesService();
export default tutorSourcesService;
