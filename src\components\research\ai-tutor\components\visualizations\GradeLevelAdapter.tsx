/**
 * Grade Level Adapter Component
 * Adapts content complexity and presentation based on student's grade level
 */

import React, { useMemo } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  GraduationCap, 
  BookOpen, 
  Lightbulb, 
  Target,
  ArrowRight,
  Star,
  Brain,
  Zap
} from "lucide-react";
import { GradeLevel, DifficultyLevel, Subject } from '../../types';

interface GradeLevelAdapterProps {
  gradeLevel: GradeLevel;
  difficulty: DifficultyLevel;
  topic: string;
  subject: Subject;
  onComplexityChange?: (newComplexity: DifficultyLevel) => void;
  className?: string;
}

interface GradeLevelContent {
  vocabulary: string[];
  concepts: string[];
  explanationStyle: string;
  visualComplexity: string;
  interactivityLevel: string;
  keyPoints: string[];
  prerequisites: string[];
  nextSteps: string[];
}

export const GradeLevelAdapter: React.FC<GradeLevelAdapterProps> = ({
  gradeLevel,
  difficulty,
  topic,
  subject,
  onComplexityChange,
  className = ''
}) => {
  const adaptedContent = useMemo((): GradeLevelContent => {
    const baseContent = {
      elementary: {
        vocabulary: ['simple', 'basic', 'easy to understand'],
        concepts: ['fundamental ideas', 'simple patterns', 'basic relationships'],
        explanationStyle: 'Simple and visual with lots of examples',
        visualComplexity: 'Basic shapes and colors',
        interactivityLevel: 'Guided exploration with hints',
        keyPoints: [
          `${topic} is an important concept in ${subject}`,
          'We can see how it works through pictures and animations',
          'Try clicking and moving things to explore!'
        ],
        prerequisites: ['Basic reading skills', 'Curiosity to learn'],
        nextSteps: ['Practice with similar examples', 'Ask questions about what you see']
      },
      middle: {
        vocabulary: ['intermediate', 'detailed', 'comprehensive'],
        concepts: ['cause and effect', 'patterns and relationships', 'multiple variables'],
        explanationStyle: 'Detailed with comparisons and real-world examples',
        visualComplexity: 'Multiple elements with clear labels',
        interactivityLevel: 'Semi-guided with exploration opportunities',
        keyPoints: [
          `Understanding ${topic} helps explain many ${subject} phenomena`,
          'Multiple factors can influence how this concept works',
          'You can experiment with different settings to see changes'
        ],
        prerequisites: [`Basic ${subject} knowledge`, 'Understanding of graphs and charts'],
        nextSteps: ['Explore advanced parameters', 'Connect to other related concepts']
      },
      high: {
        vocabulary: ['advanced', 'complex', 'sophisticated'],
        concepts: ['systems thinking', 'mathematical relationships', 'theoretical frameworks'],
        explanationStyle: 'Comprehensive with mathematical and theoretical depth',
        visualComplexity: 'Detailed diagrams with multiple data layers',
        interactivityLevel: 'Free exploration with advanced controls',
        keyPoints: [
          `${topic} demonstrates key principles of ${subject}`,
          'Mathematical relationships govern the behavior you observe',
          'Advanced controls allow for detailed parameter exploration'
        ],
        prerequisites: [`Strong ${subject} foundation`, 'Mathematical reasoning skills'],
        nextSteps: ['Analyze mathematical relationships', 'Apply to real-world problems']
      },
      college: {
        vocabulary: ['technical', 'precise', 'research-level'],
        concepts: ['theoretical models', 'quantitative analysis', 'research applications'],
        explanationStyle: 'Technical with research context and applications',
        visualComplexity: 'Research-grade visualizations with data analysis',
        interactivityLevel: 'Professional-level tools and analysis',
        keyPoints: [
          `${topic} represents a fundamental aspect of ${subject} theory`,
          'Quantitative analysis reveals underlying mathematical principles',
          'Research applications demonstrate real-world significance'
        ],
        prerequisites: [`Advanced ${subject} coursework`, 'Research methodology'],
        nextSteps: ['Conduct independent research', 'Apply to thesis projects']
      }
    };

    return baseContent[gradeLevel] || baseContent.high;
  }, [gradeLevel, topic, subject]);

  const getGradeLevelIcon = (level: GradeLevel) => {
    const icons = {
      elementary: Star,
      middle: BookOpen,
      high: Brain,
      college: GraduationCap
    };
    return icons[level] || Brain;
  };

  const getGradeLevelColor = (level: GradeLevel) => {
    const colors = {
      elementary: 'bg-green-100 text-green-800 border-green-200',
      middle: 'bg-blue-100 text-blue-800 border-blue-200',
      high: 'bg-purple-100 text-purple-800 border-purple-200',
      college: 'bg-orange-100 text-orange-800 border-orange-200'
    };
    return colors[level] || colors.high;
  };

  const getDifficultyIcon = (diff: DifficultyLevel) => {
    const icons = {
      beginner: Lightbulb,
      intermediate: Target,
      advanced: Zap
    };
    return icons[diff] || Target;
  };

  const availableComplexities: DifficultyLevel[] = useMemo(() => {
    const complexityMap = {
      elementary: ['beginner'] as DifficultyLevel[],
      middle: ['beginner', 'intermediate'] as DifficultyLevel[],
      high: ['beginner', 'intermediate', 'advanced'] as DifficultyLevel[],
      college: ['intermediate', 'advanced'] as DifficultyLevel[]
    };
    return complexityMap[gradeLevel] || ['intermediate'];
  }, [gradeLevel]);

  const GradeLevelIcon = getGradeLevelIcon(gradeLevel);
  const DifficultyIcon = getDifficultyIcon(difficulty);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={className}
    >
      <Card className="border-0 bg-gradient-to-r from-blue-50 to-purple-50">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm flex items-center space-x-2">
              <GradeLevelIcon className="w-4 h-4" />
              <span>Adapted for {gradeLevel.charAt(0).toUpperCase() + gradeLevel.slice(1)} Level</span>
            </CardTitle>
            <div className="flex items-center space-x-2">
              <Badge className={getGradeLevelColor(gradeLevel)}>
                {gradeLevel}
              </Badge>
              <Badge variant="outline">
                <DifficultyIcon className="w-3 h-3 mr-1" />
                {difficulty}
              </Badge>
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Explanation Style */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Learning Approach</h4>
            <p className="text-sm text-gray-600">{adaptedContent.explanationStyle}</p>
          </div>

          {/* Key Points */}
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-gray-700">Key Learning Points</h4>
            <ul className="space-y-1">
              {adaptedContent.keyPoints.map((point, index) => (
                <li key={index} className="text-sm text-gray-600 flex items-start space-x-2">
                  <ArrowRight className="w-3 h-3 mt-0.5 text-blue-500 flex-shrink-0" />
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </div>

          {/* Complexity Adjustment */}
          {onComplexityChange && availableComplexities.length > 1 && (
            <div className="space-y-2">
              <h4 className="text-sm font-medium text-gray-700">Adjust Complexity</h4>
              <div className="flex items-center space-x-2">
                {availableComplexities.map((complexity) => {
                  const ComplexityIcon = getDifficultyIcon(complexity);
                  return (
                    <Button
                      key={complexity}
                      variant={difficulty === complexity ? "secondary" : "outline"}
                      size="sm"
                      onClick={() => onComplexityChange(complexity)}
                      className="h-8 px-3 text-xs"
                    >
                      <ComplexityIcon className="w-3 h-3 mr-1" />
                      {complexity}
                    </Button>
                  );
                })}
              </div>
            </div>
          )}

          {/* Prerequisites & Next Steps */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-2 border-t">
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Prerequisites
              </h4>
              <ul className="space-y-1">
                {adaptedContent.prerequisites.map((prereq, index) => (
                  <li key={index} className="text-xs text-gray-600">
                    • {prereq}
                  </li>
                ))}
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                Next Steps
              </h4>
              <ul className="space-y-1">
                {adaptedContent.nextSteps.map((step, index) => (
                  <li key={index} className="text-xs text-gray-600">
                    • {step}
                  </li>
                ))}
              </ul>
            </div>
          </div>

          {/* Visual Complexity Info */}
          <div className="bg-white rounded-lg p-3 border">
            <div className="flex items-center space-x-2 mb-2">
              <Target className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium text-gray-700">Visualization Features</span>
            </div>
            <div className="grid grid-cols-2 gap-2 text-xs text-gray-600">
              <div>
                <span className="font-medium">Visual Style:</span> {adaptedContent.visualComplexity}
              </div>
              <div>
                <span className="font-medium">Interaction:</span> {adaptedContent.interactivityLevel}
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
};
