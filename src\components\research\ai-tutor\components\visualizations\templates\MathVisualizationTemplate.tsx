/**
 * Math Visualization Template
 * Interactive mathematical visualizations for educational purposes
 */

import React, { useState, useEffect, useMemo } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Play, Pause, RotateCcw, Settings, Calculator } from "lucide-react";

interface MathVisualizationProps {
  type: 'function' | 'derivative' | 'integral' | 'trigonometry' | 'geometry' | 'statistics';
  title: string;
  description: string;
  educationLevel: 'elementary' | 'middle-school' | 'high-school' | 'college';
  onParameterChange?: (parameter: string, value: number) => void;
}

interface MathState {
  isAnimating: boolean;
  time: number;
  parameters: Record<string, number>;
  function: string;
  domain: { min: number; max: number };
  range: { min: number; max: number };
}

export const MathVisualizationTemplate: React.FC<MathVisualizationProps> = ({
  type,
  title,
  description,
  educationLevel,
  onParameterChange
}) => {
  const [mathState, setMathState] = useState<MathState>({
    isAnimating: false,
    time: 0,
    parameters: getDefaultParameters(type),
    function: getDefaultFunction(type),
    domain: { min: -10, max: 10 },
    range: { min: -10, max: 10 }
  });
  
  const [showControls, setShowControls] = useState(false);
  const [customFunction, setCustomFunction] = useState('');

  // Animation loop for time-based functions
  useEffect(() => {
    if (mathState.isAnimating) {
      const interval = setInterval(() => {
        setMathState(prev => ({ ...prev, time: prev.time + 0.1 }));
      }, 50);
      return () => clearInterval(interval);
    }
  }, [mathState.isAnimating]);

  const toggleAnimation = () => {
    setMathState(prev => ({ ...prev, isAnimating: !prev.isAnimating }));
  };

  const resetVisualization = () => {
    setMathState(prev => ({ ...prev, time: 0, isAnimating: false }));
  };

  const updateParameter = (parameter: string, value: number[]) => {
    const newValue = value[0];
    setMathState(prev => ({
      ...prev,
      parameters: { ...prev.parameters, [parameter]: newValue }
    }));
    onParameterChange?.(parameter, newValue);
  };

  const evaluateFunction = (x: number): number => {
    const { parameters, time } = mathState;
    
    switch (type) {
      case 'function':
        const a = parameters.a || 1;
        const b = parameters.b || 0;
        const c = parameters.c || 0;
        return a * x * x + b * x + c;
      
      case 'trigonometry':
        const amplitude = parameters.amplitude || 1;
        const frequency = parameters.frequency || 1;
        const phase = parameters.phase || 0;
        return amplitude * Math.sin(frequency * x + phase + time);
      
      case 'derivative':
        // Show function and its derivative
        const func = parameters.a * x * x + parameters.b * x + parameters.c;
        return parameters.showDerivative ? 2 * parameters.a * x + parameters.b : func;
      
      default:
        return Math.sin(x);
    }
  };

  const generatePoints = useMemo(() => {
    const points = [];
    const step = (mathState.domain.max - mathState.domain.min) / 200;
    
    for (let x = mathState.domain.min; x <= mathState.domain.max; x += step) {
      try {
        const y = evaluateFunction(x);
        if (isFinite(y)) {
          // Convert to screen coordinates
          const screenX = ((x - mathState.domain.min) / (mathState.domain.max - mathState.domain.min)) * 400;
          const screenY = 200 - ((y - mathState.range.min) / (mathState.range.max - mathState.range.min)) * 200;
          points.push({ x: screenX, y: screenY, mathX: x, mathY: y });
        }
      } catch (error) {
        // Skip invalid points
      }
    }
    
    return points;
  }, [mathState.parameters, mathState.time, mathState.domain, mathState.range, type]);

  const renderVisualization = () => {
    switch (type) {
      case 'function':
      case 'trigonometry':
      case 'derivative':
        return <FunctionGraph points={generatePoints} mathState={mathState} type={type} />;
      case 'geometry':
        return <GeometryVisualization mathState={mathState} />;
      case 'statistics':
        return <StatisticsVisualization mathState={mathState} />;
      default:
        return <FunctionGraph points={generatePoints} mathState={mathState} type={type} />;
    }
  };

  const renderControls = () => {
    const params = getParameterConfig(type, educationLevel);
    
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium text-gray-900">Math Controls</h4>
        
        {params.map((param) => (
          <div key={param.key} className="space-y-2">
            <div className="flex justify-between items-center">
              <label className="text-sm font-medium text-gray-700">
                {param.label}
              </label>
              <span className="text-sm text-gray-500">
                {mathState.parameters[param.key]?.toFixed(2)}
              </span>
            </div>
            <Slider
              value={[mathState.parameters[param.key] || param.default]}
              onValueChange={(value) => updateParameter(param.key, value)}
              min={param.min}
              max={param.max}
              step={param.step}
              className="w-full"
            />
            <p className="text-xs text-gray-600">{param.description}</p>
          </div>
        ))}

        {/* Custom Function Input for Advanced Users */}
        {educationLevel === 'college' && (
          <div className="space-y-2">
            <Label htmlFor="custom-function">Custom Function</Label>
            <Input
              id="custom-function"
              placeholder="e.g., sin(x) + cos(2*x)"
              value={customFunction}
              onChange={(e) => setCustomFunction(e.target.value)}
              className="text-sm"
            />
            <p className="text-xs text-gray-600">
              Use x as variable. Supported: sin, cos, tan, log, exp, sqrt, abs
            </p>
          </div>
        )}
      </div>
    );
  };

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg flex items-center space-x-2">
              <Calculator className="w-5 h-5 text-blue-500" />
              <span>{title}</span>
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">{description}</p>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline">{type}</Badge>
            <Badge variant="outline">{educationLevel}</Badge>
          </div>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Math Visualization Display */}
        <div className="w-full h-80 bg-gradient-to-br from-green-50 to-blue-50 rounded-lg relative overflow-hidden border-2 border-green-200">
          {renderVisualization()}
          
          {/* Control Overlay */}
          <div className="absolute top-4 left-4 flex items-center space-x-2">
            <Button
              variant="secondary"
              size="sm"
              onClick={toggleAnimation}
              className="bg-white/90 backdrop-blur-sm"
            >
              {mathState.isAnimating ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={resetVisualization}
              className="bg-white/90 backdrop-blur-sm"
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
            <Button
              variant="secondary"
              size="sm"
              onClick={() => setShowControls(!showControls)}
              className="bg-white/90 backdrop-blur-sm"
            >
              <Settings className="w-4 h-4" />
            </Button>
          </div>

          {/* Function Display */}
          <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-1">
            <span className="text-sm font-mono">
              {getFunctionDisplay(type, mathState.parameters)}
            </span>
          </div>
        </div>

        {/* Interactive Controls */}
        {showControls && renderControls()}

        {/* Mathematical Concepts */}
        <div className="bg-green-50 rounded-lg p-4">
          <h4 className="font-medium text-green-900 mb-2">Mathematical Concepts</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <h5 className="font-medium text-green-800 mb-1">Key Ideas:</h5>
              <ul className="text-green-700 space-y-1">
                {getKeyIdeas(type, educationLevel).map((idea, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>{idea}</span>
                  </li>
                ))}
              </ul>
            </div>
            <div>
              <h5 className="font-medium text-green-800 mb-1">Try This:</h5>
              <ul className="text-green-700 space-y-1">
                {getInteractivePrompts(type, educationLevel).map((prompt, index) => (
                  <li key={index} className="flex items-start space-x-2">
                    <span className="text-green-600 mt-1">•</span>
                    <span>{prompt}</span>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Visualization Components
const FunctionGraph: React.FC<{ 
  points: Array<{x: number, y: number, mathX: number, mathY: number}>, 
  mathState: MathState,
  type: string 
}> = ({ points, mathState, type }) => {
  return (
    <div className="w-full h-full relative">
      {/* Coordinate System */}
      <svg className="absolute inset-0 w-full h-full">
        {/* Grid Lines */}
        <defs>
          <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#grid)" />
        
        {/* Axes */}
        <line x1="0" y1="200" x2="400" y2="200" stroke="#6b7280" strokeWidth="2" />
        <line x1="200" y1="0" x2="200" y2="400" stroke="#6b7280" strokeWidth="2" />
        
        {/* Axis Labels */}
        <text x="390" y="195" className="text-xs fill-gray-600">x</text>
        <text x="205" y="15" className="text-xs fill-gray-600">y</text>
        
        {/* Function Curve */}
        {points.length > 1 && (
          <polyline
            points={points.map(p => `${p.x},${p.y}`).join(' ')}
            fill="none"
            stroke="#10b981"
            strokeWidth="3"
            className="drop-shadow-sm"
          />
        )}
        
        {/* Animated Point */}
        {mathState.isAnimating && points.length > 0 && (
          <motion.circle
            cx={points[Math.floor((mathState.time * 10) % points.length)]?.x || 0}
            cy={points[Math.floor((mathState.time * 10) % points.length)]?.y || 0}
            r="4"
            fill="#ef4444"
            className="drop-shadow-sm"
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 0.5, repeat: Infinity }}
          />
        )}
      </svg>
    </div>
  );
};

const GeometryVisualization: React.FC<{ mathState: MathState }> = ({ mathState }) => {
  const { parameters } = mathState;
  const radius = parameters.radius || 50;
  const sides = Math.floor(parameters.sides || 6);
  
  // Generate polygon points
  const polygonPoints = [];
  for (let i = 0; i < sides; i++) {
    const angle = (2 * Math.PI * i) / sides;
    const x = 200 + radius * Math.cos(angle);
    const y = 200 + radius * Math.sin(angle);
    polygonPoints.push(`${x},${y}`);
  }
  
  return (
    <div className="w-full h-full relative">
      <svg className="absolute inset-0 w-full h-full">
        {/* Grid */}
        <defs>
          <pattern id="geo-grid" width="20" height="20" patternUnits="userSpaceOnUse">
            <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="1"/>
          </pattern>
        </defs>
        <rect width="100%" height="100%" fill="url(#geo-grid)" />
        
        {/* Polygon */}
        <polygon
          points={polygonPoints.join(' ')}
          fill="rgba(59, 130, 246, 0.2)"
          stroke="#3b82f6"
          strokeWidth="2"
        />
        
        {/* Center Point */}
        <circle cx="200" cy="200" r="3" fill="#ef4444" />
        
        {/* Radius Lines */}
        {polygonPoints.map((point, index) => {
          const [x, y] = point.split(',').map(Number);
          return (
            <line
              key={index}
              x1="200" y1="200"
              x2={x} y2={y}
              stroke="#6b7280"
              strokeWidth="1"
              strokeDasharray="5,5"
            />
          );
        })}
      </svg>
    </div>
  );
};

const StatisticsVisualization: React.FC<{ mathState: MathState }> = ({ mathState }) => {
  const { parameters } = mathState;
  const mean = parameters.mean || 0;
  const stdDev = parameters.stdDev || 1;
  
  // Generate normal distribution curve
  const points = [];
  for (let x = -4; x <= 4; x += 0.1) {
    const y = (1 / (stdDev * Math.sqrt(2 * Math.PI))) * 
              Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2));
    const screenX = ((x + 4) / 8) * 400;
    const screenY = 300 - (y * 200);
    points.push(`${screenX},${screenY}`);
  }
  
  return (
    <div className="w-full h-full relative">
      <svg className="absolute inset-0 w-full h-full">
        {/* Axes */}
        <line x1="0" y1="300" x2="400" y2="300" stroke="#6b7280" strokeWidth="2" />
        <line x1="200" y1="0" x2="200" y2="400" stroke="#6b7280" strokeWidth="2" />
        
        {/* Normal Distribution Curve */}
        <polyline
          points={points.join(' ')}
          fill="none"
          stroke="#8b5cf6"
          strokeWidth="3"
        />
        
        {/* Mean Line */}
        <line
          x1={200 + mean * 50} y1="0"
          x2={200 + mean * 50} y2="400"
          stroke="#ef4444"
          strokeWidth="2"
          strokeDasharray="5,5"
        />
      </svg>
    </div>
  );
};

// Helper Functions
function getDefaultParameters(type: string): Record<string, number> {
  switch (type) {
    case 'function':
      return { a: 1, b: 0, c: 0 };
    case 'trigonometry':
      return { amplitude: 1, frequency: 1, phase: 0 };
    case 'derivative':
      return { a: 1, b: 0, c: 0, showDerivative: 0 };
    case 'geometry':
      return { radius: 50, sides: 6 };
    case 'statistics':
      return { mean: 0, stdDev: 1 };
    default:
      return {};
  }
}

function getDefaultFunction(type: string): string {
  switch (type) {
    case 'function':
      return 'ax² + bx + c';
    case 'trigonometry':
      return 'A·sin(fx + φ)';
    case 'derivative':
      return "f'(x) = 2ax + b";
    default:
      return 'f(x)';
  }
}

function getParameterConfig(type: string, educationLevel: string) {
  const configs = {
    function: [
      { key: 'a', label: 'Coefficient a', min: -5, max: 5, step: 0.1, default: 1, description: 'Controls the width and direction of the parabola' },
      { key: 'b', label: 'Coefficient b', min: -10, max: 10, step: 0.5, default: 0, description: 'Shifts the parabola horizontally' },
      { key: 'c', label: 'Coefficient c', min: -10, max: 10, step: 0.5, default: 0, description: 'Shifts the parabola vertically' }
    ],
    trigonometry: [
      { key: 'amplitude', label: 'Amplitude', min: 0.1, max: 3, step: 0.1, default: 1, description: 'Height of the wave' },
      { key: 'frequency', label: 'Frequency', min: 0.1, max: 3, step: 0.1, default: 1, description: 'How many cycles per unit' },
      { key: 'phase', label: 'Phase', min: 0, max: 6.28, step: 0.1, default: 0, description: 'Horizontal shift of the wave' }
    ],
    geometry: [
      { key: 'radius', label: 'Radius', min: 20, max: 100, step: 5, default: 50, description: 'Distance from center to vertex' },
      { key: 'sides', label: 'Sides', min: 3, max: 12, step: 1, default: 6, description: 'Number of sides in the polygon' }
    ]
  };
  
  return configs[type] || [];
}

function getFunctionDisplay(type: string, parameters: Record<string, number>): string {
  switch (type) {
    case 'function':
      const a = parameters.a || 1;
      const b = parameters.b || 0;
      const c = parameters.c || 0;
      return `f(x) = ${a.toFixed(1)}x² + ${b.toFixed(1)}x + ${c.toFixed(1)}`;
    case 'trigonometry':
      const amp = parameters.amplitude || 1;
      const freq = parameters.frequency || 1;
      const phase = parameters.phase || 0;
      return `f(x) = ${amp.toFixed(1)}sin(${freq.toFixed(1)}x + ${phase.toFixed(1)})`;
    default:
      return 'f(x)';
  }
}

function getKeyIdeas(type: string, educationLevel: string): string[] {
  const ideas = {
    function: {
      'middle-school': ['Functions show relationships between numbers', 'Changing coefficients changes the shape'],
      'high-school': ['Quadratic functions form parabolas', 'The vertex is the highest or lowest point'],
      college: ['Quadratic functions have the form ax² + bx + c', 'The discriminant determines the number of roots']
    },
    trigonometry: {
      'high-school': ['Sine waves repeat in cycles', 'Amplitude controls the height'],
      college: ['Trigonometric functions model periodic phenomena', 'Phase shift moves the wave horizontally']
    }
  };
  
  return ideas[type]?.[educationLevel] || ['Explore mathematical relationships'];
}

function getInteractivePrompts(type: string, educationLevel: string): string[] {
  const prompts = {
    function: ['Change the "a" value and see what happens', 'Make the parabola open downward', 'Move the vertex to different positions'],
    trigonometry: ['Adjust amplitude to make taller waves', 'Change frequency to see more cycles', 'Use phase to shift the wave']
  };
  
  return prompts[type] || ['Experiment with the controls'];
}

export default MathVisualizationTemplate;
