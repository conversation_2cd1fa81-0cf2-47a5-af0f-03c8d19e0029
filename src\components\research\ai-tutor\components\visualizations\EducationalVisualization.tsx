/**
 * Educational Visualization Component
 * Main wrapper for all educational visualizations with grade-level adaptation
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Play, 
  Pause, 
  RotateCcw, 
  Settings, 
  BookOpen, 
  Eye,
  Lightbulb,
  Target
} from "lucide-react";
import { 
  EducationalVisualizationProps, 
  AnimationControls,
  VisualizationAnalytics 
} from '../../types';
import { AnimationControlsComponent } from './AnimationControls';
import { GradeLevelAdapter } from './GradeLevelAdapter';
import { useTutorStore } from '../../stores/tutor.store';

interface EducationalVisualizationState {
  isInitialized: boolean;
  hasError: boolean;
  errorMessage?: string;
  analytics: Partial<VisualizationAnalytics>;
}

export const EducationalVisualization: React.FC<EducationalVisualizationProps> = ({
  topic,
  gradeLevel,
  difficulty = 'intermediate',
  interactive = true,
  subject,
  parameters = {},
  onParameterChange,
  onComplete,
  className = ''
}) => {
  const { 
    visualizationState, 
    updateAnimationControls,
    addVisualizationToHistory 
  } = useTutorStore();

  const [state, setState] = useState<EducationalVisualizationState>({
    isInitialized: false,
    hasError: false,
    analytics: {
      startTime: new Date(),
      interactionCount: 0,
      pauseCount: 0,
      replayCount: 0,
      speedChanges: 0,
      parametersChanged: []
    }
  });

  const [showExplanation, setShowExplanation] = useState(true);
  const [showControls, setShowControls] = useState(interactive);

  // Initialize visualization
  useEffect(() => {
    const timer = setTimeout(() => {
      setState(prev => ({ ...prev, isInitialized: true }));
    }, 500);

    return () => clearTimeout(timer);
  }, []);

  // Track analytics
  useEffect(() => {
    const startTime = new Date();
    setState(prev => ({
      ...prev,
      analytics: { ...prev.analytics, startTime }
    }));

    return () => {
      // Save analytics on unmount
      const endTime = new Date();
      const totalTimeSpent = (endTime.getTime() - startTime.getTime()) / 1000;
      
      // Add to history
      if (visualizationState.currentVisualization) {
        addVisualizationToHistory({
          id: `history-${Date.now()}`,
          visualization: visualizationState.currentVisualization,
          sessionId: 'current', // This would come from current session
          timestamp: new Date(),
          timeSpent: totalTimeSpent,
          interactionCount: state.analytics.interactionCount || 0,
          completed: true
        });
      }
    };
  }, []);

  const handleAnimationControl = useCallback((action: string) => {
    const { animationControls } = visualizationState;
    
    switch (action) {
      case 'play':
        updateAnimationControls({ isPlaying: true });
        break;
      case 'pause':
        updateAnimationControls({ isPlaying: false });
        setState(prev => ({
          ...prev,
          analytics: { 
            ...prev.analytics, 
            pauseCount: (prev.analytics.pauseCount || 0) + 1 
          }
        }));
        break;
      case 'reset':
        updateAnimationControls({ 
          isPlaying: false, 
          currentStep: 0 
        });
        setState(prev => ({
          ...prev,
          analytics: { 
            ...prev.analytics, 
            replayCount: (prev.analytics.replayCount || 0) + 1 
          }
        }));
        break;
    }

    // Track interaction
    setState(prev => ({
      ...prev,
      analytics: { 
        ...prev.analytics, 
        interactionCount: (prev.analytics.interactionCount || 0) + 1 
      }
    }));
  }, [visualizationState, updateAnimationControls]);

  const handleSpeedChange = useCallback((speed: number) => {
    updateAnimationControls({ speed });
    setState(prev => ({
      ...prev,
      analytics: { 
        ...prev.analytics, 
        speedChanges: (prev.analytics.speedChanges || 0) + 1 
      }
    }));
  }, [updateAnimationControls]);

  const handleParameterChange = useCallback((key: string, value: any) => {
    if (onParameterChange) {
      onParameterChange(key, value);
    }

    setState(prev => ({
      ...prev,
      analytics: { 
        ...prev.analytics, 
        parametersChanged: [
          ...(prev.analytics.parametersChanged || []),
          key
        ]
      }
    }));
  }, [onParameterChange]);

  const getSubjectColor = (subject: string) => {
    const colors = {
      physics: 'bg-blue-500',
      mathematics: 'bg-green-500',
      chemistry: 'bg-purple-500',
      biology: 'bg-emerald-500',
      'computer-science': 'bg-orange-500',
      engineering: 'bg-red-500',
      economics: 'bg-yellow-500',
      general: 'bg-gray-500'
    };
    return colors[subject as keyof typeof colors] || colors.general;
  };

  const getDifficultyColor = (difficulty: string) => {
    const colors = {
      beginner: 'bg-green-100 text-green-800',
      intermediate: 'bg-yellow-100 text-yellow-800',
      advanced: 'bg-red-100 text-red-800'
    };
    return colors[difficulty as keyof typeof colors] || colors.intermediate;
  };

  if (state.hasError) {
    return (
      <Card className={`border-red-200 ${className}`}>
        <CardContent className="p-6 text-center">
          <div className="text-red-500 mb-2">
            <Eye className="w-8 h-8 mx-auto" />
          </div>
          <h3 className="text-lg font-semibold text-red-700 mb-2">
            Visualization Error
          </h3>
          <p className="text-red-600 mb-4">
            {state.errorMessage || 'Failed to load visualization'}
          </p>
          <Button 
            variant="outline" 
            onClick={() => setState(prev => ({ ...prev, hasError: false }))}
          >
            <RotateCcw className="w-4 h-4 mr-2" />
            Try Again
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Visualization Header */}
      <Card className="border-0 shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className={`w-3 h-3 rounded-full ${getSubjectColor(subject)}`} />
              <CardTitle className="text-lg capitalize">
                {subject.replace('-', ' ')} Visualization
              </CardTitle>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline" className={getDifficultyColor(difficulty)}>
                {difficulty}
              </Badge>
              <Badge variant="outline">
                {gradeLevel}
              </Badge>
            </div>
          </div>
          <p className="text-sm text-gray-600 mt-2">
            Interactive visualization for: <strong>{topic}</strong>
          </p>
        </CardHeader>
      </Card>

      {/* Grade Level Adapter */}
      <GradeLevelAdapter
        gradeLevel={gradeLevel}
        difficulty={difficulty}
        topic={topic}
        subject={subject}
      />

      {/* Main Visualization Area */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <AnimatePresence>
            {!state.isInitialized ? (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="flex items-center justify-center h-64"
              >
                <div className="text-center">
                  <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4" />
                  <p className="text-gray-600">Loading visualization...</p>
                </div>
              </motion.div>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="space-y-4"
              >
                {/* Placeholder for actual visualization */}
                <div className="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8 text-center border-2 border-dashed border-blue-200">
                  <div className="mb-4">
                    <Lightbulb className="w-12 h-12 mx-auto text-blue-500" />
                  </div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-2">
                    {topic} Visualization
                  </h3>
                  <p className="text-gray-600 mb-4">
                    Interactive {subject} visualization will appear here
                  </p>
                  <div className="flex items-center justify-center space-x-4 text-sm text-gray-500">
                    <div className="flex items-center space-x-1">
                      <Target className="w-4 h-4" />
                      <span>Grade: {gradeLevel}</span>
                    </div>
                    <div className="flex items-center space-x-1">
                      <Settings className="w-4 h-4" />
                      <span>Level: {difficulty}</span>
                    </div>
                  </div>
                </div>

                {/* Animation Controls */}
                {showControls && (
                  <AnimationControlsComponent
                    controls={visualizationState.animationControls}
                    onPlay={() => handleAnimationControl('play')}
                    onPause={() => handleAnimationControl('pause')}
                    onReset={() => handleAnimationControl('reset')}
                    onSpeedChange={handleSpeedChange}
                    interactive={interactive}
                  />
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </CardContent>
      </Card>

      {/* Learning Objectives */}
      {showExplanation && (
        <Card className="border-0 shadow-sm">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm flex items-center space-x-2">
              <BookOpen className="w-4 h-4" />
              <span>Learning Objectives</span>
            </CardTitle>
          </CardHeader>
          <CardContent className="pt-0">
            <ul className="text-sm text-gray-600 space-y-1">
              <li>• Understand the concept of {topic}</li>
              <li>• Visualize how {subject} principles apply</li>
              <li>• Interact with the visualization to explore different scenarios</li>
              <li>• Connect visual elements to theoretical knowledge</li>
            </ul>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
