/**
 * Safe Code Executor
 * Safely executes AI-generated visualization code in a sandboxed environment
 */

import React, { useState, useEffect, useMemo, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Button } from "@/components/ui/button";
import { AlertTriangle, Play, Pause, RotateCcw, Code, Eye } from "lucide-react";
import { CodeDisplayPanel } from '../code/CodeDisplayPanel';
import * as Babel from '@babel/standalone';

interface SafeCodeExecutorProps {
  code: string;
  type: 'react' | 'html' | 'd3' | 'plotly' | 'three';
  title: string;
  description: string;
  dependencies: string[];
  className?: string;
  onError?: (error: string) => void;
  onSuccess?: () => void;
}

interface ExecutionResult {
  success: boolean;
  error?: string;
  component?: React.ComponentType<any>;
  html?: string;
}

export const SafeCodeExecutor: React.FC<SafeCodeExecutorProps> = ({
  code,
  type,
  title,
  description,
  dependencies,
  className = '',
  onError,
  onSuccess
}) => {
  const [executionResult, setExecutionResult] = useState<ExecutionResult | null>(null);
  const [isExecuting, setIsExecuting] = useState(false);
  const [showCode, setShowCode] = useState(false);
  const [isPlaying, setIsPlaying] = useState(true);
  const containerRef = useRef<HTMLDivElement>(null);

  // Execute code safely when component mounts or code changes
  useEffect(() => {
    executeCode();
  }, [code, type]);

  const executeCode = async () => {
    setIsExecuting(true);
    setExecutionResult(null);

    console.log('SafeCodeExecutor: Starting execution', { type, codeLength: code.length });

    try {
      const result = await safeExecute(code, type, dependencies);
      console.log('SafeCodeExecutor: Execution result', result);
      setExecutionResult(result);

      if (result.success) {
        console.log('SafeCodeExecutor: Execution successful');
        onSuccess?.();
      } else {
        console.error('SafeCodeExecutor: Execution failed', result.error);
        onError?.(result.error || 'Unknown execution error');
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Code execution failed';
      console.error('SafeCodeExecutor: Exception during execution', error);
      setExecutionResult({ success: false, error: errorMessage });
      onError?.(errorMessage);
    } finally {
      setIsExecuting(false);
    }
  };

  const safeExecute = async (
    code: string, 
    type: string, 
    dependencies: string[]
  ): Promise<ExecutionResult> => {
    try {
      switch (type) {
        case 'react':
          return await executeReactCode(code, dependencies);
        case 'html':
          return executeHTMLCode(code);
        case 'd3':
          return await executeD3Code(code);
        case 'plotly':
          return await executePlotlyCode(code);
        default:
          return { success: false, error: `Unsupported code type: ${type}` };
      }
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'Execution failed' 
      };
    }
  };

  const executeReactCode = async (code: string, dependencies: string[]): Promise<ExecutionResult> => {
    try {
      // Validate that the code is safe React code
      if (!isValidReactCode(code)) {
        console.error('Code validation failed for:', code.substring(0, 200) + '...');
        console.error('Full code:', code);
        throw new Error('Code contains potentially unsafe patterns. Please ensure your code uses only safe React patterns.');
      }

      // Create a comprehensive universal execution context for ANY topic
      const safeGlobals = {
        // Core React
        React,
        useState: React.useState,
        useEffect: React.useEffect,
        useCallback: React.useCallback,
        useMemo: React.useMemo,
        useRef: React.useRef,
        useReducer: React.useReducer,
        useContext: React.useContext,

        // Animation & Motion
        motion,
        AnimatePresence: motion.AnimatePresence,
        MotionDiv: motion.div,
        MotionSpan: motion.span,
        MotionButton: motion.button,
        MotionPath: motion.path,
        MotionSvg: motion.svg,
        MotionCircle: motion.circle,
        MotionRect: motion.rect,
        MotionLine: motion.line,
        MotionPolygon: motion.polygon,

        // Mathematical & Scientific Computing
        Math,

        // Data Manipulation & Analysis
        // Simulated D3-like utilities for data visualization
        d3: {
          scaleLinear: (domain: number[], range: number[]) => {
            return (value: number) => {
              const [d0, d1] = domain;
              const [r0, r1] = range;
              return r0 + (value - d0) * (r1 - r0) / (d1 - d0);
            };
          },
          scaleOrdinal: (range: any[]) => {
            return (value: any, index: number) => range[index % range.length];
          },
          scaleBand: (domain: any[], range: number[]) => {
            const bandwidth = (range[1] - range[0]) / domain.length;
            return Object.assign(
              (value: any) => range[0] + domain.indexOf(value) * bandwidth,
              { bandwidth: () => bandwidth }
            );
          },
          extent: (data: number[]) => [Math.min(...data), Math.max(...data)],
          max: (data: number[]) => Math.max(...data),
          min: (data: number[]) => Math.min(...data),
          sum: (data: number[]) => data.reduce((a, b) => a + b, 0),
          mean: (data: number[]) => data.reduce((a, b) => a + b, 0) / data.length,
          median: (data: number[]) => {
            const sorted = [...data].sort((a, b) => a - b);
            const mid = Math.floor(sorted.length / 2);
            return sorted.length % 2 ? sorted[mid] : (sorted[mid - 1] + sorted[mid]) / 2;
          },
          range: (start: number, stop?: number, step = 1) => {
            if (stop === undefined) { stop = start; start = 0; }
            const result = [];
            for (let i = start; i < stop; i += step) result.push(i);
            return result;
          },
          interpolate: (a: any, b: any) => (t: number) => a + (b - a) * t,
          color: (color: string) => ({ toString: () => color }),
          schemeCategory10: ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'],
          schemeSet3: ['#8dd3c7', '#ffffb3', '#bebada', '#fb8072', '#80b1d3', '#fdb462', '#b3de69', '#fccde5', '#d9d9d9', '#bc80bd'],
        },

        // Chart.js-like utilities for quick charts
        Chart: {
          defaults: {
            color: '#666',
            font: { size: 12 },
            responsive: true,
            maintainAspectRatio: false
          }
        },

        // Economic & Financial utilities
        Economics: {
          calculateCompoundInterest: (principal: number, rate: number, time: number, compound = 1) =>
            principal * Math.pow(1 + rate / compound, compound * time),
          calculateSimpleInterest: (principal: number, rate: number, time: number) =>
            principal * (1 + rate * time),
          calculateNPV: (cashFlows: number[], discountRate: number) =>
            cashFlows.reduce((npv, cf, i) => npv + cf / Math.pow(1 + discountRate, i), 0),
          calculateIRR: (cashFlows: number[]) => {
            // Simplified IRR calculation
            let rate = 0.1;
            for (let i = 0; i < 100; i++) {
              const npv = cashFlows.reduce((sum, cf, j) => sum + cf / Math.pow(1 + rate, j), 0);
              if (Math.abs(npv) < 0.01) return rate;
              rate += npv > 0 ? 0.01 : -0.01;
            }
            return rate;
          },
          supplyDemandEquilibrium: (supply: (p: number) => number, demand: (p: number) => number) => {
            for (let p = 0; p < 1000; p += 0.1) {
              if (Math.abs(supply(p) - demand(p)) < 0.1) return { price: p, quantity: supply(p) };
            }
            return null;
          }
        },

        // Statistics & Probability
        Stats: {
          normalDistribution: (x: number, mean = 0, stdDev = 1) =>
            Math.exp(-0.5 * Math.pow((x - mean) / stdDev, 2)) / (stdDev * Math.sqrt(2 * Math.PI)),
          binomialProbability: (n: number, k: number, p: number) => {
            const combination = (n: number, k: number) => {
              if (k > n) return 0;
              let result = 1;
              for (let i = 0; i < k; i++) {
                result *= (n - i) / (i + 1);
              }
              return result;
            };
            return combination(n, k) * Math.pow(p, k) * Math.pow(1 - p, n - k);
          },
          correlation: (x: number[], y: number[]) => {
            const n = x.length;
            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
            const sumY2 = y.reduce((sum, yi) => sum + yi * yi, 0);
            return (n * sumXY - sumX * sumY) / Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
          },
          regression: (x: number[], y: number[]) => {
            const n = x.length;
            const sumX = x.reduce((a, b) => a + b, 0);
            const sumY = y.reduce((a, b) => a + b, 0);
            const sumXY = x.reduce((sum, xi, i) => sum + xi * y[i], 0);
            const sumX2 = x.reduce((sum, xi) => sum + xi * xi, 0);
            const slope = (n * sumXY - sumX * sumY) / (n * sumX2 - sumX * sumX);
            const intercept = (sumY - slope * sumX) / n;
            return { slope, intercept, predict: (x: number) => slope * x + intercept };
          }
        },

        // Legal & Governance utilities
        Legal: {
          calculateDamages: (economic: number, nonEconomic: number, punitive = 0) =>
            ({ economic, nonEconomic, punitive, total: economic + nonEconomic + punitive }),
          statuteOfLimitations: (incidentDate: Date, limitYears: number) =>
            new Date(incidentDate.getTime() + limitYears * 365.25 * 24 * 60 * 60 * 1000),
          contractTerms: {
            isValidConsideration: (value: any) => value !== null && value !== undefined && value !== 0,
            calculateBreach: (expectedValue: number, actualValue: number) => expectedValue - actualValue,
            liquidatedDamages: (amount: number, isReasonable: boolean) => isReasonable ? amount : null
          }
        },

        // Social Sciences utilities
        Social: {
          demographicAnalysis: {
            populationGrowth: (initial: number, rate: number, years: number) =>
              initial * Math.pow(1 + rate, years),
            dependencyRatio: (young: number, old: number, working: number) =>
              (young + old) / working * 100,
            urbanizationRate: (urban: number, total: number) => urban / total * 100
          },
          surveyAnalysis: {
            likertScale: (responses: number[]) => ({
              mean: responses.reduce((a, b) => a + b, 0) / responses.length,
              mode: responses.sort((a, b) =>
                responses.filter(v => v === a).length - responses.filter(v => v === b).length
              ).pop(),
              distribution: responses.reduce((acc, val) => {
                acc[val] = (acc[val] || 0) + 1;
                return acc;
              }, {} as Record<number, number>)
            })
          }
        },

        // Utilities for any domain
        Utils: {
          generateData: (count: number, min = 0, max = 100) =>
            Array.from({ length: count }, () => Math.random() * (max - min) + min),
          interpolateColor: (color1: string, color2: string, factor: number) => {
            // Simple color interpolation
            const hex1 = color1.replace('#', '');
            const hex2 = color2.replace('#', '');
            const r1 = parseInt(hex1.substr(0, 2), 16);
            const g1 = parseInt(hex1.substr(2, 2), 16);
            const b1 = parseInt(hex1.substr(4, 2), 16);
            const r2 = parseInt(hex2.substr(0, 2), 16);
            const g2 = parseInt(hex2.substr(2, 2), 16);
            const b2 = parseInt(hex2.substr(4, 2), 16);
            const r = Math.round(r1 + (r2 - r1) * factor);
            const g = Math.round(g1 + (g2 - g1) * factor);
            const b = Math.round(b1 + (b2 - b1) * factor);
            return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
          },
          formatCurrency: (amount: number, currency = 'USD') =>
            new Intl.NumberFormat('en-US', { style: 'currency', currency }).format(amount),
          formatPercent: (value: number, decimals = 2) =>
            (value * 100).toFixed(decimals) + '%',
          debounce: (func: Function, wait: number) => {
            let timeout: NodeJS.Timeout;
            return (...args: any[]) => {
              clearTimeout(timeout);
              timeout = setTimeout(() => func.apply(null, args), wait);
            };
          },
          throttle: (func: Function, limit: number) => {
            let inThrottle: boolean;
            return (...args: any[]) => {
              if (!inThrottle) {
                func.apply(null, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
              }
            };
          }
        },

        // Browser APIs & Timing
        console: {
          log: (...args: any[]) => console.log('[Visualization]', ...args),
          warn: (...args: any[]) => console.warn('[Visualization]', ...args),
          error: (...args: any[]) => console.error('[Visualization]', ...args)
        },
        setTimeout,
        setInterval,
        clearTimeout,
        clearInterval,
        requestAnimationFrame,
        cancelAnimationFrame,

        // Core JavaScript
        Date,
        JSON,
        Array,
        Object,
        String,
        Number,
        Boolean,
        Map,
        Set,
        WeakMap,
        WeakSet,
        Promise,
        RegExp,
        Error,

        // Additional Math utilities
        PI: Math.PI,
        E: Math.E,
        abs: Math.abs,
        ceil: Math.ceil,
        floor: Math.floor,
        round: Math.round,
        max: Math.max,
        min: Math.min,
        pow: Math.pow,
        sqrt: Math.sqrt,
        sin: Math.sin,
        cos: Math.cos,
        tan: Math.tan,
        asin: Math.asin,
        acos: Math.acos,
        atan: Math.atan,
        atan2: Math.atan2,
        exp: Math.exp,
        log: Math.log,
        random: Math.random
      };

      // Preprocess and clean the code
      console.log('🚀 Starting React code execution...');
      console.log('Original code length:', code.length);
      console.log('Code preview:', code.substring(0, 300) + '...');

      let processedCode = preprocessCode(code);

      // Transform the code into an executable component
      const executableCode = createExecutableComponent(processedCode);
      console.log('✅ Code transformation completed');

      // Try to execute the code
      let Component;
      try {
        const componentFactory = new Function('globals', executableCode);
        Component = componentFactory(safeGlobals);
      } catch (jsxError) {
        console.error('❌ Code execution failed:', jsxError);
        return {
          success: false,
          error: `Code execution failed: ${jsxError.message}. Please try a different approach or ask for help with the specific topic.`
        };
      }

      if (typeof Component !== 'function') {
        console.error('❌ Generated code did not return a valid React component');
        return {
          success: false,
          error: 'Generated code did not return a valid React component function'
        };
      }

      console.log('✅ React component successfully created');
      return { success: true, component: Component };
    } catch (error) {
      console.error('❌ React code execution error:', error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'React code execution failed'
      };
    }
  };

  /**
   * Preprocess code to handle common issues and malformed patterns
   */
  const preprocessCode = (code: string): string => {
    let processed = code.trim();

    // Remove any leading/trailing markdown code block markers
    processed = processed.replace(/^```(?:jsx?|tsx?|javascript|typescript)?\s*/, '');
    processed = processed.replace(/```\s*$/, '');

    // Fix common malformed patterns that cause "return outside function" errors

    // Handle "return function ComponentName()" pattern (invalid) - remove the return
    processed = processed.replace(/^return\s+function\s+(\w+)/gm, 'function $1');

    // Handle "return const ComponentName =" pattern (invalid) - remove the return
    processed = processed.replace(/^return\s+const\s+(\w+)\s*=/gm, 'const $1 =');

    // Handle standalone return statements at the beginning of lines (invalid)
    processed = processed.replace(/^return\s+(?![\(\<])/gm, '');

    // Handle return statements that are not inside functions
    const lines = processed.split('\n');
    let insideFunction = false;
    let braceCount = 0;

    const fixedLines = lines.map((line, index) => {
      // Track if we're inside a function
      if (/function\s+\w+|const\s+\w+\s*=.*=>|=>\s*{/.test(line)) {
        insideFunction = true;
        braceCount = 0;
      }

      // Count braces to track function scope
      const openBraces = (line.match(/\{/g) || []).length;
      const closeBraces = (line.match(/\}/g) || []).length;
      braceCount += openBraces - closeBraces;

      if (braceCount <= 0 && insideFunction) {
        insideFunction = false;
      }

      // Fix return statements outside functions
      if (!insideFunction && /^\s*return\s+/.test(line)) {
        console.log(`🔧 Fixing return outside function at line ${index + 1}: ${line.trim()}`);
        return line.replace(/^\s*return\s+/, '');
      }

      return line;
    });

    processed = fixedLines.join('\n');

    // Ensure proper line endings
    processed = processed.replace(/\r\n/g, '\n');

    console.log('🔧 Code preprocessing completed');
    return processed;
  };

  /**
   * Create executable component code with proper structure
   */
  const createExecutableComponent = (code: string): string => {
    let cleanCode = code;

    // Remove import statements as we provide globals
    cleanCode = cleanCode.replace(/import\s+.*?from\s+['"][^'"]*['"];?\s*/g, '');
    cleanCode = cleanCode.replace(/import\s+['"][^'"]*['"];?\s*/g, '');

    // Handle different component patterns
    let componentCode = '';

    if (cleanCode.includes('export default function')) {
      // Pattern: export default function ComponentName() { ... }
      const match = cleanCode.match(/export default function\s+(\w+)/);
      if (match) {
        componentCode = cleanCode.replace('export default function', 'function');
        componentCode += `\nreturn ${match[1]};`;
      }
    } else if (cleanCode.includes('export default')) {
      // Pattern: export default ComponentName or export default () => { ... }
      if (cleanCode.includes('export default (')) {
        // Arrow function export
        componentCode = cleanCode.replace(/export default\s*/, 'return ');
      } else {
        // Named component export
        const match = cleanCode.match(/export default\s+(\w+)/);
        if (match) {
          componentCode = cleanCode.replace(/export default\s+\w+;?\s*$/, '');
          componentCode += `\nreturn ${match[1]};`;
        }
      }
    } else if (cleanCode.includes('function')) {
      // Pattern: function ComponentName() { ... } (no export)
      const match = cleanCode.match(/function\s+(\w+)/);
      if (match) {
        componentCode = cleanCode + `\nreturn ${match[1]};`;
      }
    } else if (cleanCode.includes('const') && cleanCode.includes('=>')) {
      // Pattern: const ComponentName = () => { ... } (no export)
      const match = cleanCode.match(/const\s+(\w+)\s*=/);
      if (match) {
        componentCode = cleanCode + `\nreturn ${match[1]};`;
      }
    } else {
      throw new Error('No valid React component pattern found. Please ensure your code defines a React component.');
    }

    // Transform JSX to React.createElement calls
    componentCode = transformJSXToReactCalls(componentCode);

    // Create the final executable code
    const executableCode = `
      try {
        const {
          React, useState, useEffect, useCallback, useMemo, useRef, useReducer, useContext,
          motion, AnimatePresence, MotionDiv, MotionSpan, MotionButton, MotionPath, MotionSvg, MotionCircle, MotionRect, MotionLine, MotionPolygon,
          Math, d3, Chart, Economics, Stats, Legal, Social, Utils,
          console, setTimeout, setInterval, clearTimeout, clearInterval, requestAnimationFrame, cancelAnimationFrame,
          Date, JSON, Array, Object, String, Number, Boolean, Map, Set, WeakMap, WeakSet, Promise, RegExp, Error,
          PI, E, abs, ceil, floor, round, max, min, pow, sqrt, sin, cos, tan, asin, acos, atan, atan2, exp, log, random
        } = globals;

        ${componentCode}
      } catch (error) {
        throw new Error('Component execution error: ' + error.message);
      }
    `;

    console.log('📝 Final executable code preview:', executableCode.substring(0, 400) + '...');
    return executableCode;
  };

  const executeHTMLCode = (code: string): ExecutionResult => {
    try {
      // Sanitize HTML code to prevent XSS
      const sanitizedHTML = sanitizeHTML(code);
      return { success: true, html: sanitizedHTML };
    } catch (error) {
      return { 
        success: false, 
        error: error instanceof Error ? error.message : 'HTML execution failed' 
      };
    }
  };

  const executeD3Code = async (code: string): Promise<ExecutionResult> => {
    // For now, return a placeholder for D3 visualizations
    return { 
      success: false, 
      error: 'D3 visualizations not yet supported in safe execution environment' 
    };
  };

  const executePlotlyCode = async (code: string): Promise<ExecutionResult> => {
    // For now, return a placeholder for Plotly visualizations
    return { 
      success: false, 
      error: 'Plotly visualizations not yet supported in safe execution environment' 
    };
  };



  /**
   * Transform JSX to React.createElement using Babel with robust fallbacks
   */
  const transformJSXToReactCalls = (code: string): string => {
    try {
      console.log('🔄 Transforming JSX using Babel...');

      // First, try to fix common syntax issues before Babel transformation
      let preprocessedCode = code;

      // Fix common JSX syntax issues
      preprocessedCode = preprocessedCode.replace(/className=/g, 'className=');
      preprocessedCode = preprocessedCode.replace(/onClick=/g, 'onClick=');

      // Use Babel to transform JSX to React.createElement calls
      const result = Babel.transform(preprocessedCode, {
        presets: [
          ['react', {
            pragma: 'React.createElement',
            pragmaFrag: 'React.Fragment'
          }]
        ],
        plugins: []
      });

      console.log('✅ Babel JSX transformation completed');
      console.log('Transformed preview:', result.code?.substring(0, 500));

      return result.code || code;
    } catch (error) {
      console.warn('⚠️ Babel transformation failed, trying enhanced fallback:', error);

      // Enhanced fallback transformation
      try {
        let transformed = code;

        // Handle self-closing tags
        transformed = transformed.replace(
          /<(\w+)([^>]*?)\/>/g,
          (match, tagName, attributes) => {
            const props = parseAttributes(attributes);
            return `React.createElement('${tagName}', ${props})`;
          }
        );

        // Handle opening/closing tag pairs
        transformed = transformed.replace(
          /<(\w+)([^>]*?)>([\s\S]*?)<\/\1>/g,
          (match, tagName, attributes, children) => {
            const props = parseAttributes(attributes);
            const processedChildren = children.trim() ? `, ${processChildren(children)}` : '';
            return `React.createElement('${tagName}', ${props}${processedChildren})`;
          }
        );

        console.log('✅ Enhanced fallback transformation completed');
        return transformed;
      } catch (fallbackError) {
        console.error('❌ All transformations failed, returning original code:', fallbackError);
        // Return original code - it might work if it's already valid JS
        return code;
      }
    }
  };

  /**
   * Parse JSX attributes into props object
   */
  const parseAttributes = (attributes: string): string => {
    if (!attributes || !attributes.trim()) {
      return 'null';
    }

    try {
      const props: string[] = [];
      const attrRegex = /(\w+)=(?:{([^}]+)}|"([^"]*)"|'([^']*)')/g;
      let match;

      while ((match = attrRegex.exec(attributes)) !== null) {
        const [, name, jsValue, stringValue1, stringValue2] = match;
        const value = jsValue || `"${stringValue1 || stringValue2}"`;
        props.push(`${name}: ${value}`);
      }

      // Handle boolean attributes (like disabled, checked)
      const booleanAttrs = attributes.replace(attrRegex, '').trim().split(/\s+/).filter(Boolean);
      booleanAttrs.forEach(attr => {
        if (attr && !props.some(p => p.startsWith(attr + ':'))) {
          props.push(`${attr}: true`);
        }
      });

      return props.length > 0 ? `{${props.join(', ')}}` : 'null';
    } catch (error) {
      console.warn('Failed to parse attributes, using null:', error);
      return 'null';
    }
  };

  /**
   * Process JSX children content
   */
  const processChildren = (children: string): string => {
    const trimmed = children.trim();

    // If it's just text, return as string
    if (!trimmed.includes('<') && !trimmed.includes('{')) {
      return `"${trimmed.replace(/"/g, '\\"')}"`;
    }

    // If it contains JSX or expressions, return as-is (will be processed recursively)
    return trimmed;
  };

  const isValidReactCode = (code: string): boolean => {
    // Very permissive validation - only block truly dangerous patterns
    const dangerousPatterns = [
      /eval\s*\(/,
      /Function\s*\(/,
      /new\s+Function/,
      /document\.write/,
      /document\.createElement\s*\(\s*['"]script/,
      /innerHTML\s*=.*<script/i,
      /dangerouslySetInnerHTML.*<script/i,
      /window\.location\s*=/,
      /window\.open/,
      /require\s*\(\s*['"]fs/,
      /require\s*\(\s*['"]child_process/,
      /require\s*\(\s*['"]net/,
      /require\s*\(\s*['"]http/,
      /require\s*\(\s*['"]crypto/,
      /require\s*\(\s*['"]os/,
      /require\s*\(\s*['"]path/,
      /process\.exit/,
      /process\.kill/,
      /__proto__\s*=/,
      /\.constructor\s*\(\s*['"][^'"]*eval/,
      /XMLHttpRequest/,
      /webkitRequestFileSystem/,
      /navigator\.geolocation/,
      /navigator\.camera/,
      /navigator\.microphone/,
      /import\s+.*\s+from\s+['"]http/,
      /import\s+.*\s+from\s+['"]https/,
      /import\s+.*\s+from\s+['"]file:/
    ];

    // Allow virtually all educational and visualization patterns
    const allowedPatterns = [
      // React patterns
      /import\s+React/,
      /import\s+\{[^}]*\}\s+from\s+['"]react['"]/,
      /export\s+default\s+function/,
      /export\s+default/,
      /function\s+\w+/,
      /const\s+\w+\s*=/,
      /let\s+\w+\s*=/,
      /var\s+\w+\s*=/,

      // React hooks
      /useState/,
      /useEffect/,
      /useCallback/,
      /useMemo/,
      /useRef/,
      /useReducer/,
      /useContext/,

      // Animation & Motion
      /motion\./,
      /AnimatePresence/,
      /Motion\w+/,
      /animate=/,
      /initial=/,
      /transition=/,
      /whileHover=/,
      /whileTap=/,
      /whileInView=/,
      /variants=/,

      // Data & Visualization
      /d3\./,
      /Chart\./,
      /Economics\./,
      /Stats\./,
      /Legal\./,
      /Social\./,
      /Utils\./,

      // Mathematical operations
      /Math\./,
      /sin\(/,
      /cos\(/,
      /tan\(/,
      /sqrt\(/,
      /pow\(/,
      /abs\(/,
      /random\(/,

      // DOM & Events
      /className=/,
      /style=/,
      /onClick=/,
      /onChange=/,
      /onMouseEnter=/,
      /onMouseLeave=/,
      /onMouseMove=/,
      /onMouseDown=/,
      /onMouseUp=/,
      /onKeyDown=/,
      /onKeyUp=/,
      /onFocus=/,
      /onBlur=/,
      /onSubmit=/,
      /onInput=/,

      // SVG & Graphics
      /svg/i,
      /path/i,
      /circle/i,
      /rect/i,
      /line/i,
      /polygon/i,
      /polyline/i,
      /ellipse/i,
      /g>/i,
      /defs>/i,
      /marker/i,

      // Canvas operations
      /canvas/i,
      /getContext/,
      /fillRect/,
      /strokeRect/,
      /arc/,
      /beginPath/,
      /closePath/,
      /fill\(/,
      /stroke\(/,

      // Data manipulation
      /map\(/,
      /filter\(/,
      /reduce\(/,
      /forEach\(/,
      /find\(/,
      /sort\(/,
      /slice\(/,
      /splice\(/,
      /push\(/,
      /pop\(/,
      /shift\(/,
      /unshift\(/,

      // Timing & Animation
      /setTimeout/,
      /setInterval/,
      /requestAnimationFrame/,
      /cancelAnimationFrame/,

      // Console & Debugging
      /console\./,

      // Basic JavaScript
      /return\s+/,
      /if\s*\(/,
      /else/,
      /for\s*\(/,
      /while\s*\(/,
      /switch\s*\(/,
      /case\s+/,
      /default:/,
      /try\s*\{/,
      /catch\s*\(/,
      /finally\s*\{/,
      /throw\s+/,

      // JSX patterns
      /<\w+/,
      /<\/\w+>/,
      /\{.*\}/,

      // Common educational content
      /visualization/i,
      /animation/i,
      /interactive/i,
      /educational/i,
      /learning/i,
      /tutorial/i,
      /example/i,
      /demo/i,
      /simulation/i,
      /model/i,
      /chart/i,
      /graph/i,
      /diagram/i,
      /plot/i
    ];

    // Check for dangerous patterns
    const hasDangerousCode = dangerousPatterns.some(pattern => pattern.test(code));

    // Must have at least one allowed pattern for React components
    const hasValidReactCode = allowedPatterns.some(pattern => pattern.test(code)) ||
                              code.includes('export default') ||
                              code.includes('function') ||
                              code.includes('const') ||
                              code.includes('return');

    return !hasDangerousCode && hasValidReactCode;
  };

  const sanitizeHTML = (html: string): string => {
    // Basic HTML sanitization - remove script tags and dangerous attributes
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/on\w+\s*=\s*["'][^"']*["']/gi, '')
      .replace(/javascript:/gi, '');
  };

  const renderVisualization = () => {
    if (isExecuting) {
      return (
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-2" />
            <p className="text-sm text-gray-600">Executing visualization...</p>
          </div>
        </div>
      );
    }

    if (!executionResult) {
      return (
        <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
          <p className="text-sm text-gray-500">No visualization to display</p>
        </div>
      );
    }

    if (!executionResult.success) {
      return (
        <Alert className="m-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Execution Error:</strong> {executionResult.error}
          </AlertDescription>
        </Alert>
      );
    }

    if (executionResult.component) {
      const Component = executionResult.component;
      return (
        <div className="w-full h-64 bg-gradient-to-br from-gray-50 to-blue-50 rounded-lg relative overflow-hidden">
          <React.Suspense fallback={
            <div className="flex items-center justify-center h-full">
              <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
            </div>
          }>
            <Component isPlaying={isPlaying} />
          </React.Suspense>
        </div>
      );
    }

    if (executionResult.html) {
      return (
        <div 
          className="w-full h-64 bg-white rounded-lg border overflow-auto"
          dangerouslySetInnerHTML={{ __html: executionResult.html }}
        />
      );
    }

    return (
      <div className="flex items-center justify-center h-64 bg-gray-50 rounded-lg">
        <p className="text-sm text-gray-500">Visualization executed but no content to display</p>
      </div>
    );
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Controls */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setIsPlaying(!isPlaying)}
            disabled={!executionResult?.success}
          >
            {isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={executeCode}
            disabled={isExecuting}
          >
            <RotateCcw className="w-4 h-4" />
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowCode(!showCode)}
          >
            {showCode ? <Eye className="w-4 h-4" /> : <Code className="w-4 h-4" />}
          </Button>
        </div>
      </div>

      {/* Visualization or Code Display */}
      <AnimatePresence mode="wait">
        {showCode ? (
          <motion.div
            key="code"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
          >
            <CodeDisplayPanel
              code={code}
              language={type === 'react' ? 'typescript' : type}
              title={title}
              description={description}
              educationLevel="intermediate"
              onExecute={executeCode}
              className="border-0 shadow-none"
            />
          </motion.div>
        ) : (
          <motion.div
            key="visualization"
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
          >
            {renderVisualization()}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default SafeCodeExecutor;
