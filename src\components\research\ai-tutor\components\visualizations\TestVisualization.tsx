/**
 * Test Visualization Component
 * Simple test to verify the SafeCodeExecutor works correctly
 */

import React from 'react';
import { SafeCodeExecutor } from './SafeCodeExecutor';

const testCode = `export default function TestVisualization() {
  const [count, setCount] = useState(0);
  const [isAnimating, setIsAnimating] = useState(true);

  useEffect(() => {
    if (isAnimating) {
      const interval = setInterval(() => {
        setCount(prev => (prev + 1) % 10);
      }, 500);
      return () => clearInterval(interval);
    }
  }, [isAnimating]);

  return (
    <div className="w-full h-64 bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg flex flex-col items-center justify-center border-2 border-blue-200">
      <div className="text-center">
        <h3 className="text-2xl font-bold text-gray-800 mb-4">Test Visualization</h3>
        
        <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center text-white text-2xl font-bold mb-4">
          {count}
        </div>
        
        <button
          onClick={() => setIsAnimating(!isAnimating)}
          className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors"
        >
          {isAnimating ? 'Pause' : 'Play'} Animation
        </button>
        
        <div className="mt-4 text-sm text-gray-600">
          <p>This is a test visualization to verify the SafeCodeExecutor works!</p>
          <p>Counter: {count} | Status: {isAnimating ? 'Running' : 'Paused'}</p>
        </div>
      </div>
    </div>
  );
}`;

export const TestVisualization: React.FC = () => {
  return (
    <div className="p-6">
      <h2 className="text-xl font-bold mb-4">Visualization Test</h2>
      <SafeCodeExecutor
        code={testCode}
        type="react"
        title="Test Visualization"
        description="A simple test to verify the visualization system works"
        onError={(error) => {
          console.error('Test visualization error:', error);
          alert('Test failed: ' + error);
        }}
        onSuccess={() => {
          console.log('Test visualization successful!');
        }}
      />
    </div>
  );
};

export default TestVisualization;
