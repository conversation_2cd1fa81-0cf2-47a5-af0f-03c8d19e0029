/**
 * Career Visualization Component
 * Interactive grid-based visualization of career paths
 */

import React, { useMemo } from 'react';
import { CareerPath } from '../types';
import { DIFFICULTY_LEVELS } from '../constants';

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Target,
  TrendingUp,
  Clock,
  DollarSign,
  BarChart3,
  Briefcase,
  ChevronRight,
  Award
} from "lucide-react";

interface CareerVisualizationProps {
  careerPaths: CareerPath[];
  onCareerSelect: (career: CareerPath) => void;
  className?: string;
}

export function CareerVisualization({
  careerPaths,
  onCareerSelect,
  className
}: CareerVisualizationProps) {

  // Calculate statistics
  const stats = useMemo(() => {
    if (!careerPaths || careerPaths.length === 0) {
      return { avgTimeline: 0, difficultyDistribution: {}, salaryRanges: [] };
    }

    const difficultyDistribution = careerPaths.reduce((acc, career) => {
      acc[career.difficulty] = (acc[career.difficulty] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const salaryRanges = careerPaths.map(career => career.salary).filter(Boolean);

    return {
      totalCareers: careerPaths.length,
      difficultyDistribution,
      salaryRanges
    };
  }, [careerPaths]);

  const getDifficultyIcon = (difficulty: string) => {
    switch (difficulty) {
      case 'Low':
        return <TrendingUp className="h-4 w-4 text-green-600" />;
      case 'Medium':
        return <TrendingUp className="h-4 w-4 text-orange-600" />;
      case 'High':
        return <TrendingUp className="h-4 w-4 text-red-600" />;
      default:
        return <TrendingUp className="h-4 w-4 text-gray-600" />;
    }
  };

  if (!careerPaths || careerPaths.length === 0) {
    return (
      <Card className="w-full h-96 flex items-center justify-center">
        <CardContent className="text-center">
          <Target className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">No Career Paths Generated</h3>
          <p className="text-gray-600">
            Upload your resume and generate career analysis to see your personalized career paths.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Statistics Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Target className="h-8 w-8 text-blue-500 mr-3" />
              <div>
                <p className="text-2xl font-bold text-gray-900">{stats.totalCareers}</p>
                <p className="text-sm text-gray-600">Career Paths</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <BarChart3 className="h-8 w-8 text-green-500 mr-3" />
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.difficultyDistribution['Low'] || 0}
                </p>
                <p className="text-sm text-gray-600">Easy Transitions</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <Clock className="h-8 w-8 text-orange-500 mr-3" />
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.difficultyDistribution['Medium'] || 0}
                </p>
                <p className="text-sm text-gray-600">Medium Effort</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <TrendingUp className="h-8 w-8 text-red-500 mr-3" />
              <div>
                <p className="text-2xl font-bold text-gray-900">
                  {stats.difficultyDistribution['High'] || 0}
                </p>
                <p className="text-sm text-gray-600">High Growth</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Interactive Career Network */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Target className="h-5 w-5 mr-2 text-blue-600" />
            Your Career Network
          </CardTitle>
          <p className="text-sm text-gray-600">
            Click on any career path to explore detailed information, roadmaps, and requirements.
          </p>
        </CardHeader>
        <CardContent className="p-6">
          <div className="relative">
            {/* Center Hub */}
            <div className="flex justify-center mb-8">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-4 rounded-full shadow-lg">
                <div className="flex items-center space-x-2">
                  <Briefcase className="h-6 w-6" />
                  <span className="font-semibold text-lg">Your Career Paths</span>
                </div>
              </div>
            </div>

            {/* Career Paths Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {careerPaths.map((career, index) => (
                <div key={career.id} className="relative">
                  {/* Connection Line */}
                  <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 w-0.5 h-8 bg-gradient-to-b from-blue-300 to-transparent"></div>

                  {/* Career Card */}
                  <Card
                    className="cursor-pointer hover:shadow-xl transition-all duration-300 hover:scale-105 border-2 hover:border-blue-300"
                    onClick={() => onCareerSelect(career)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-center space-x-2">
                          <div className="p-2 bg-blue-100 rounded-lg">
                            <Briefcase className="h-4 w-4 text-blue-600" />
                          </div>
                          <Badge
                            variant="secondary"
                            className={`text-xs ${DIFFICULTY_LEVELS[career.difficulty].color}`}
                          >
                            <span className="flex items-center space-x-1">
                              {getDifficultyIcon(career.difficulty)}
                              <span>{career.difficulty}</span>
                            </span>
                          </Badge>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-400 hover:text-blue-500 transition-colors" />
                      </div>

                      <h3 className="font-semibold text-gray-900 text-sm leading-tight mb-2 hover:text-blue-600 transition-colors">
                        {career.jobTitle}
                      </h3>

                      <p className="text-xs text-gray-600 mb-3 line-clamp-2">
                        {career.jobDescription}
                      </p>

                      <div className="space-y-2">
                        <div className="flex items-center justify-between text-xs">
                          <span className="flex items-center text-gray-500">
                            <Clock className="h-3 w-3 mr-1" />
                            Timeline:
                          </span>
                          <span className="font-medium">{career.timeline}</span>
                        </div>
                        <div className="flex items-center justify-between text-xs">
                          <span className="flex items-center text-gray-500">
                            <DollarSign className="h-3 w-3 mr-1" />
                            Salary:
                          </span>
                          <span className="font-medium">{career.salary}</span>
                        </div>
                        {career.skills && career.skills.length > 0 && (
                          <div className="mt-3">
                            <p className="text-xs text-gray-500 mb-1">Key Skills:</p>
                            <div className="flex flex-wrap gap-1">
                              {career.skills.slice(0, 2).map((skill, skillIndex) => (
                                <Badge key={skillIndex} variant="outline" className="text-xs px-1 py-0">
                                  {skill.length > 12 ? skill.substring(0, 12) + '...' : skill}
                                </Badge>
                              ))}
                              {career.skills.length > 2 && (
                                <Badge variant="outline" className="text-xs px-1 py-0 bg-blue-50 text-blue-600 border-blue-200">
                                  +{career.skills.length - 2}
                                </Badge>
                              )}
                            </div>
                          </div>
                        )}
                      </div>

                      <div className="mt-4 pt-3 border-t border-gray-100">
                        <Button variant="ghost" size="sm" className="w-full text-xs">
                          <Award className="h-3 w-3 mr-1" />
                          Explore Career Path
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>



      {/* Instructions */}
      <Card className="bg-blue-50 border-blue-200">
        <CardContent className="p-4">
          <div className="flex items-start">
            <Target className="h-5 w-5 text-blue-600 mr-3 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-1">How to Explore Your Career Paths</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Click on any career card to view detailed information and roadmaps</li>
                <li>• Review difficulty levels: Green (Easy), Orange (Medium), Red (High)</li>
                <li>• Compare timelines, salaries, and required skills across careers</li>
                <li>• Use the export feature to save your career analysis</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
