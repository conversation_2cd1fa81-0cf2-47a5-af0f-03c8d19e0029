/**
 * Resume Upload Form Component
 * Handles file upload, context input, and AI model selection
 */

import React, { useCallback, useState, useRef } from 'react';
import { ResumeData } from '../types';
import { AI_MODELS, UPLOAD_CONFIG } from '../constants';
import { pdfParserService } from '../services/pdf-parser.service';

import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { 
  Upload, 
  FileText, 
  CheckCircle, 
  AlertCircle, 
  Sparkles,
  Brain,
  Target
} from "lucide-react";

interface ResumeUploadFormProps {
  onResumeUpload: (file: File) => void;
  onContextChange: (context: string) => void;
  onModelChange: (model: string) => void;
  onGenerate: () => void;
  isLoading: boolean;
  error: string | null;
  resumeData: ResumeData | null;
  additionalContext: string;
  selectedModel: string;
}

export function ResumeUploadForm({
  onResumeUpload,
  onContextChange,
  onModelChange,
  onGenerate,
  isLoading,
  error,
  resumeData,
  additionalContext,
  selectedModel
}: ResumeUploadFormProps) {
  const [dragActive, setDragActive] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = useCallback((files: FileList | null) => {
    if (files && files.length > 0) {
      const file = files[0];
      onResumeUpload(file);
    }
  }, [onResumeUpload]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
  }, []);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setDragActive(false);
    handleFileSelect(e.dataTransfer.files);
  }, [handleFileSelect]);

  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    handleFileSelect(e.target.files);
  }, [handleFileSelect]);

  const handleClick = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  const canGenerate = resumeData && !isLoading;

  return (
    <div className="max-w-4xl mx-auto space-y-6">
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 border-blue-200">
        <CardContent className="p-6">
          <div className="text-center">
            <div className="flex justify-center mb-4">
              <div className="p-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
                <Target className="h-8 w-8 text-white" />
              </div>
            </div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2">
              Discover Your Ideal Career Path
            </h2>
            <p className="text-gray-600 max-w-2xl mx-auto">
              Upload your resume and let our AI analyze your skills, experience, and potential 
              to suggest personalized career opportunities with detailed roadmaps.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Resume Upload */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Upload className="h-5 w-5 mr-2 text-blue-600" />
            Upload Your Resume
          </CardTitle>
        </CardHeader>
        <CardContent>
          {!resumeData ? (
            <div
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onDrop={handleDrop}
              onClick={handleClick}
              className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                dragActive
                  ? 'border-blue-500 bg-blue-50'
                  : 'border-gray-300 hover:border-gray-400'
              }`}
            >
              <input
                ref={fileInputRef}
                type="file"
                accept=".pdf,.doc,.docx,.txt"
                onChange={handleInputChange}
                className="hidden"
              />
              <div className="flex flex-col items-center">
                <Upload className={`h-12 w-12 mb-4 ${
                  dragActive ? 'text-blue-500' : 'text-gray-400'
                }`} />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">
                  {dragActive ? 'Drop your resume here' : 'Upload your resume'}
                </h3>
                <p className="text-gray-600 mb-4">
                  Drag and drop your file here, or click to browse
                </p>
                <div className="flex flex-wrap justify-center gap-2 mb-4">
                  <Badge variant="secondary">PDF</Badge>
                  <Badge variant="secondary">Word</Badge>
                  <Badge variant="secondary">Text</Badge>
                </div>
                <p className="text-sm text-gray-500">
                  Maximum file size: 10MB
                </p>
              </div>
            </div>
          ) : (
            <div className="flex items-center justify-between p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center">
                <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                <div>
                  <p className="font-medium text-green-900">{resumeData.fileName}</p>
                  <p className="text-sm text-green-700">
                    Uploaded {resumeData.uploadedAt.toLocaleDateString()}
                  </p>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => window.location.reload()}
              >
                Change File
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Additional Context */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Brain className="h-5 w-5 mr-2 text-purple-600" />
            Additional Context (Optional)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label htmlFor="context">
              Tell us about your career interests, goals, or preferences
            </Label>
            <Textarea
              id="context"
              placeholder="e.g., I'm interested in remote work opportunities in technology, particularly in AI/ML. I prefer collaborative environments and want to make a positive impact..."
              value={additionalContext}
              onChange={(e) => onContextChange(e.target.value)}
              rows={4}
              className="resize-none"
            />
            <p className="text-sm text-gray-500">
              This helps our AI provide more personalized career recommendations.
            </p>
          </div>
        </CardContent>
      </Card>

      {/* AI Model Selection */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Sparkles className="h-5 w-5 mr-2 text-yellow-600" />
            AI Model Selection
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <Label htmlFor="model">Choose AI model for analysis</Label>
            <Select value={selectedModel} onValueChange={onModelChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select AI model" />
              </SelectTrigger>
              <SelectContent>
                {AI_MODELS.map((model) => (
                  <SelectItem key={model.id} value={model.id}>
                    <div className="flex items-center justify-between w-full">
                      <span className="font-medium">{model.name}</span>
                      <Badge variant="outline" className="ml-2">
                        {model.provider}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              {AI_MODELS.map((model) => (
                <div
                  key={model.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                    selectedModel === model.id
                      ? 'border-blue-500 bg-blue-50'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => onModelChange(model.id)}
                >
                  <h4 className="font-medium text-sm">{model.name}</h4>
                  <p className="text-xs text-gray-600 mt-1">
                    {model.capabilities.join(', ')}
                  </p>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Generate Button */}
      <div className="text-center">
        <Button
          onClick={onGenerate}
          disabled={!canGenerate}
          size="lg"
          className="px-8 py-3 text-lg"
        >
          {isLoading ? (
            <>
              <Sparkles className="h-5 w-5 mr-2 animate-spin" />
              Analyzing Resume...
            </>
          ) : (
            <>
              <Target className="h-5 w-5 mr-2" />
              Discover My Career Paths
            </>
          )}
        </Button>
        {!resumeData && (
          <p className="text-sm text-gray-500 mt-2">
            Please upload your resume to continue
          </p>
        )}
      </div>

      {/* Resume Preview */}
      {resumeData && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2 text-gray-600" />
              Resume Preview
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium text-sm text-gray-900 mb-2">Extracted Skills</h4>
                <div className="flex flex-wrap gap-1">
                  {resumeData.extractedInfo.skills.slice(0, 8).map((skill, index) => (
                    <Badge key={index} variant="secondary" className="text-xs">
                      {skill.length > 30 ? skill.substring(0, 30) + '...' : skill}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <h4 className="font-medium text-sm text-gray-900 mb-2">Experience</h4>
                <div className="space-y-1">
                  {resumeData.extractedInfo.experience.slice(0, 3).map((exp, index) => (
                    <p key={index} className="text-xs text-gray-600">
                      {exp.length > 60 ? exp.substring(0, 60) + '...' : exp}
                    </p>
                  ))}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
