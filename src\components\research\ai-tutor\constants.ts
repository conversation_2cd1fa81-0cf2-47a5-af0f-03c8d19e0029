import {
  GraduationCap,
  BookO<PERSON>,
  Calculator,
  Atom,
  Globe,
  Palette,
  Music,
  Heart,
  Briefcase,
  Code,
  Brain,
  Microscope,
  Languages,
  Trophy,
  Lightbulb,
  Users,
  Zap,
  Target,
  Star,
  Coffee
} from "lucide-react";
import { EducationLevelOption, TopicSuggestion, TutorAIModel, EducationLevel } from "./types";

// Education levels configuration
export const EDUCATION_LEVELS: EducationLevelOption[] = [
  {
    id: 'elementary',
    name: 'Elementary School',
    description: 'Ages 6-11, basic concepts with simple explanations',
    icon: BookOpen,
    ageRange: '6-11 years',
    complexity: 2
  },
  {
    id: 'middle-school',
    name: 'Middle School',
    description: 'Ages 11-14, intermediate concepts with examples',
    icon: GraduationCap,
    ageRange: '11-14 years',
    complexity: 4
  },
  {
    id: 'high-school',
    name: 'High School',
    description: 'Ages 14-18, advanced concepts with applications',
    icon: Trophy,
    ageRange: '14-18 years',
    complexity: 6
  },
  {
    id: 'college',
    name: 'College',
    description: 'Ages 18-22, university-level depth and analysis',
    icon: Brain,
    ageRange: '18-22 years',
    complexity: 8
  },
  {
    id: 'undergraduate',
    name: 'Undergraduate',
    description: 'Bachelor\'s degree level, comprehensive understanding',
    icon: Target,
    ageRange: '18-25 years',
    complexity: 8
  },
  {
    id: 'graduate',
    name: 'Graduate',
    description: 'Master\'s/PhD level, research and specialized knowledge',
    icon: Star,
    ageRange: '22+ years',
    complexity: 10
  },
  {
    id: 'professional',
    name: 'Professional',
    description: 'Working professionals, practical applications',
    icon: Briefcase,
    ageRange: 'Any age',
    complexity: 9
  }
];

// Topic suggestions organized by category
export const TOPIC_SUGGESTIONS: TopicSuggestion[] = [
  // Mathematics
  {
    id: 'algebra-basics',
    name: 'Algebra Basics',
    category: 'Mathematics',
    icon: '🔢',
    description: 'Learn fundamental algebraic concepts and equations',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: ['basic arithmetic'],
    relatedTopics: ['geometry', 'calculus']
  },
  {
    id: 'calculus-intro',
    name: 'Introduction to Calculus',
    category: 'Mathematics',
    icon: '📈',
    description: 'Understand derivatives and integrals',
    difficulty: 'intermediate',
    estimatedTime: '60 minutes',
    prerequisites: ['algebra', 'trigonometry'],
    relatedTopics: ['physics', 'engineering']
  },
  {
    id: 'statistics',
    name: 'Statistics & Probability',
    category: 'Mathematics',
    icon: '📊',
    description: 'Learn data analysis and probability theory',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['data science', 'research methods']
  },

  // Science
  {
    id: 'chemistry-basics',
    name: 'Chemistry Fundamentals',
    category: 'Science',
    icon: '⚗️',
    description: 'Explore atoms, molecules, and chemical reactions',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['physics', 'biology']
  },
  {
    id: 'physics-mechanics',
    name: 'Physics: Mechanics',
    category: 'Science',
    icon: '⚡',
    description: 'Understand motion, forces, and energy',
    difficulty: 'intermediate',
    estimatedTime: '55 minutes',
    prerequisites: ['algebra', 'trigonometry'],
    relatedTopics: ['engineering', 'astronomy']
  },
  {
    id: 'biology-cells',
    name: 'Cell Biology',
    category: 'Science',
    icon: '🧬',
    description: 'Learn about cellular structure and function',
    difficulty: 'beginner',
    estimatedTime: '35 minutes',
    prerequisites: [],
    relatedTopics: ['genetics', 'biochemistry']
  },

  // Technology
  {
    id: 'programming-python',
    name: 'Python Programming',
    category: 'Technology',
    icon: '🐍',
    description: 'Learn Python programming from scratch',
    difficulty: 'beginner',
    estimatedTime: '60 minutes',
    prerequisites: [],
    relatedTopics: ['data science', 'web development']
  },
  {
    id: 'ai-machine-learning',
    name: 'Machine Learning Basics',
    category: 'Technology',
    icon: '🤖',
    description: 'Introduction to AI and machine learning concepts',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['programming', 'statistics'],
    relatedTopics: ['data science', 'neural networks']
  },
  {
    id: 'web-development',
    name: 'Web Development',
    category: 'Technology',
    icon: '🌐',
    description: 'Build websites with HTML, CSS, and JavaScript',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: [],
    relatedTopics: ['programming', 'design']
  },

  // Languages
  {
    id: 'spanish-basics',
    name: 'Spanish Basics',
    category: 'Languages',
    icon: '🇪🇸',
    description: 'Learn fundamental Spanish vocabulary and grammar',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: [],
    relatedTopics: ['culture', 'travel']
  },
  {
    id: 'english-grammar',
    name: 'English Grammar',
    category: 'Languages',
    icon: '📝',
    description: 'Master English grammar rules and usage',
    difficulty: 'intermediate',
    estimatedTime: '35 minutes',
    prerequisites: ['basic english'],
    relatedTopics: ['writing', 'literature']
  },

  // History & Social Studies
  {
    id: 'world-war-2',
    name: 'World War II',
    category: 'History',
    icon: '🌍',
    description: 'Understand the causes, events, and impact of WWII',
    difficulty: 'intermediate',
    estimatedTime: '50 minutes',
    prerequisites: ['basic history'],
    relatedTopics: ['politics', 'geography']
  },
  {
    id: 'economics-basics',
    name: 'Economics Fundamentals',
    category: 'Social Studies',
    icon: '💰',
    description: 'Learn supply and demand, markets, and economic principles',
    difficulty: 'intermediate',
    estimatedTime: '45 minutes',
    prerequisites: ['basic math'],
    relatedTopics: ['business', 'politics']
  },

  // Arts & Literature
  {
    id: 'art-history',
    name: 'Art History',
    category: 'Arts',
    icon: '🎨',
    description: 'Explore major art movements and famous artists',
    difficulty: 'beginner',
    estimatedTime: '40 minutes',
    prerequisites: [],
    relatedTopics: ['culture', 'history']
  },
  {
    id: 'creative-writing',
    name: 'Creative Writing',
    category: 'Literature',
    icon: '✍️',
    description: 'Develop storytelling and creative writing skills',
    difficulty: 'beginner',
    estimatedTime: '45 minutes',
    prerequisites: ['basic writing'],
    relatedTopics: ['literature', 'english']
  }
];

// AI models optimized for tutoring - includes all models from open-deep-research
export const TUTOR_AI_MODELS: TutorAIModel[] = [
  // Google Models (Direct Gemini API)
  {
    id: "google/gemini-2.5-flash",
    name: "Gemini 2.5 Flash (Recommended)",
    provider: "Google",
    apiProvider: "gemini",
    description: "Fast, efficient model perfect for interactive tutoring",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Speed', 'Conversational', 'Educational content'],
    bestFor: ['Quick explanations', 'Interactive learning', 'Q&A sessions'],
    educationLevels: ['elementary', 'middle-school', 'high-school', 'college']
  },
  {
    id: "google/gemini-2.5-pro",
    name: "Gemini 2.5 Pro",
    provider: "Google",
    apiProvider: "gemini",
    description: "Advanced reasoning for complex educational content",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Advanced reasoning', 'Multimodal', 'Research synthesis'],
    bestFor: ['Complex problems', 'Research projects', 'Advanced concepts'],
    educationLevels: ['college', 'undergraduate', 'graduate', 'professional']
  },

  // OpenRouter Models
  {
    id: "anthropic/claude-3.5-sonnet",
    name: "Claude 3.5 Sonnet",
    provider: "Anthropic",
    apiProvider: "openrouter",
    description: "Excellent for detailed explanations and complex topics",
    maxTokens: 200000,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Detailed explanations', 'Complex reasoning', 'Patient teaching'],
    bestFor: ['Advanced topics', 'Step-by-step learning', 'Research guidance'],
    educationLevels: ['high-school', 'college', 'undergraduate', 'graduate']
  },
  {
    id: "anthropic/claude-3-haiku",
    name: "Claude 3 Haiku",
    provider: "Anthropic",
    apiProvider: "openrouter",
    description: "Fast and efficient for quick tutoring sessions",
    maxTokens: 200000,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Speed', 'Efficiency', 'Clear explanations'],
    bestFor: ['Quick questions', 'Basic concepts', 'Homework help'],
    educationLevels: ['elementary', 'middle-school', 'high-school']
  },
  {
    id: "openai/gpt-4o",
    name: "GPT-4o",
    provider: "OpenAI",
    apiProvider: "openrouter",
    description: "Versatile model with strong educational capabilities",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'high',
    strengths: ['Versatility', 'Code explanation', 'Creative teaching'],
    bestFor: ['Programming', 'Creative subjects', 'Personalized learning'],
    educationLevels: ['middle-school', 'high-school', 'college', 'professional']
  },
  {
    id: "openai/gpt-4o-mini",
    name: "GPT-4o Mini",
    provider: "OpenAI",
    apiProvider: "openrouter",
    description: "Efficient and cost-effective for educational tasks",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Cost-effective', 'Good reasoning', 'Educational content'],
    bestFor: ['General tutoring', 'Homework help', 'Study assistance'],
    educationLevels: ['elementary', 'middle-school', 'high-school', 'college']
  },
  {
    id: "google/gemini-2.0-flash-001",
    name: "Gemini 2.0 Flash",
    provider: "Google",
    apiProvider: "openrouter",
    description: "Latest Gemini model via OpenRouter",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Latest features', 'Fast responses', 'Multimodal'],
    bestFor: ['Modern tutoring', 'Interactive learning', 'Visual explanations'],
    educationLevels: ['middle-school', 'high-school', 'college']
  },
  {
    id: "google/gemini-pro",
    name: "Gemini Pro",
    provider: "Google",
    apiProvider: "openrouter",
    description: "Professional-grade Gemini via OpenRouter",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Professional quality', 'Complex reasoning', 'Research support'],
    bestFor: ['Advanced topics', 'Research projects', 'Professional learning'],
    educationLevels: ['college', 'undergraduate', 'graduate', 'professional']
  },
  {
    id: "meta-llama/llama-3.1-405b-instruct",
    name: "Llama 3.1 405B",
    provider: "Meta",
    apiProvider: "openrouter",
    description: "Powerful open-source model for complex educational tasks",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'high',
    strengths: ['Open source', 'Large context', 'Complex reasoning'],
    bestFor: ['Research', 'Advanced mathematics', 'Complex problem solving'],
    educationLevels: ['college', 'undergraduate', 'graduate', 'professional']
  },
  {
    id: "meta-llama/llama-3.1-70b-instruct",
    name: "Llama 3.1 70B",
    provider: "Meta",
    apiProvider: "openrouter",
    description: "Balanced open-source model for educational content",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Open source', 'Good reasoning', 'Educational content'],
    bestFor: ['General tutoring', 'STEM subjects', 'Academic writing'],
    educationLevels: ['high-school', 'college', 'undergraduate']
  },
  {
    id: "deepseek/deepseek-chat",
    name: "DeepSeek Chat",
    provider: "DeepSeek",
    apiProvider: "openrouter",
    description: "Specialized model for coding and technical subjects",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'low',
    strengths: ['Coding expertise', 'Technical subjects', 'Problem solving'],
    bestFor: ['Programming', 'Computer science', 'Technical tutorials'],
    educationLevels: ['high-school', 'college', 'professional']
  },
  {
    id: "qwen/qwen-2.5-72b-instruct",
    name: "Qwen 2.5 72B",
    provider: "Alibaba",
    apiProvider: "openrouter",
    description: "Multilingual model with strong reasoning capabilities",
    maxTokens: 8192,
    supportsStreaming: true,
    cost: 'medium',
    strengths: ['Multilingual', 'Strong reasoning', 'Cultural awareness'],
    bestFor: ['Language learning', 'International studies', 'Cultural topics'],
    educationLevels: ['middle-school', 'high-school', 'college']
  }
];

// Default tutoring settings
export const DEFAULT_TUTOR_SETTINGS = {
  preferredModel: "google/gemini-2.5-flash",
  educationLevel: 'high-school' as EducationLevel,
  learningStyle: 'visual' as const,
  pace: 'normal' as const,
  includeExamples: true,
  includeQuizzes: false,
  sourcesEnabled: true,
  maxSources: 5,
  language: 'en',
  notifications: {
    sessionReminders: true,
    progressUpdates: true
  }
};

// Topic categories for organization
export const TOPIC_CATEGORIES = [
  { id: 'mathematics', name: 'Mathematics', icon: Calculator, color: 'blue' },
  { id: 'science', name: 'Science', icon: Atom, color: 'green' },
  { id: 'technology', name: 'Technology', icon: Code, color: 'purple' },
  { id: 'languages', name: 'Languages', icon: Languages, color: 'orange' },
  { id: 'history', name: 'History', icon: Globe, color: 'red' },
  { id: 'arts', name: 'Arts', icon: Palette, color: 'pink' },
  { id: 'literature', name: 'Literature', icon: BookOpen, color: 'indigo' },
  { id: 'social-studies', name: 'Social Studies', icon: Users, color: 'yellow' }
];

// Learning styles
export const LEARNING_STYLES = [
  { id: 'visual', name: 'Visual', description: 'Learn through images, diagrams, and visual aids' },
  { id: 'auditory', name: 'Auditory', description: 'Learn through listening and verbal explanations' },
  { id: 'kinesthetic', name: 'Kinesthetic', description: 'Learn through hands-on activities and practice' },
  { id: 'reading', name: 'Reading/Writing', description: 'Learn through text and written materials' }
];

// System prompts for different education levels
export const EDUCATION_LEVEL_PROMPTS = {
  elementary: "You are a friendly, patient tutor for elementary school students (ages 6-11). Use simple language, fun examples, and encourage curiosity. Break down complex ideas into very simple parts.",
  'middle-school': "You are an encouraging tutor for middle school students (ages 11-14). Use clear explanations with relatable examples. Help build confidence while introducing more complex concepts gradually.",
  'high-school': "You are a knowledgeable tutor for high school students (ages 14-18). Provide detailed explanations with real-world applications. Encourage critical thinking and prepare students for advanced learning.",
  college: "You are an expert tutor for college students (ages 18-22). Provide comprehensive explanations with academic depth. Encourage independent thinking and research skills.",
  undergraduate: "You are a scholarly tutor for undergraduate students. Provide thorough academic explanations with research context. Support critical analysis and academic writing skills.",
  graduate: "You are an advanced academic tutor for graduate students. Provide research-level explanations with scholarly depth. Support original thinking and advanced research methodologies.",
  professional: "You are a practical tutor for working professionals. Focus on real-world applications and practical skills. Provide efficient, actionable learning that can be immediately applied."
};
