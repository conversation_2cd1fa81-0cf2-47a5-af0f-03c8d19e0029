/**
 * Animation Controls Component
 * Provides play/pause, speed control, and step-through functionality for educational visualizations
 */

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { 
  Play, 
  Pause, 
  RotateCcw, 
  SkipBack, 
  SkipForward,
  Repeat,
  Settings,
  Gauge
} from "lucide-react";
import { AnimationControls } from '../../types';

interface AnimationControlsProps {
  controls: AnimationControls;
  onPlay: () => void;
  onPause: () => void;
  onReset: () => void;
  onSpeedChange: (speed: number) => void;
  onStepForward?: () => void;
  onStepBackward?: () => void;
  onToggleLoop?: () => void;
  interactive?: boolean;
  className?: string;
}

export const AnimationControlsComponent: React.FC<AnimationControlsProps> = ({
  controls,
  onPlay,
  onPause,
  onReset,
  onSpeedChange,
  onStepForward,
  onStepBackward,
  onToggleLoop,
  interactive = true,
  className = ''
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);

  const speedOptions = [
    { value: 0.1, label: '0.1x' },
    { value: 0.25, label: '0.25x' },
    { value: 0.5, label: '0.5x' },
    { value: 1, label: '1x' },
    { value: 1.5, label: '1.5x' },
    { value: 2, label: '2x' },
    { value: 3, label: '3x' }
  ];

  const getCurrentSpeedLabel = () => {
    const option = speedOptions.find(opt => opt.value === controls.speed);
    return option?.label || `${controls.speed}x`;
  };

  const handleSpeedSelect = (speed: number) => {
    onSpeedChange(speed);
  };

  const getProgressPercentage = () => {
    if (controls.totalSteps === 0) return 0;
    return (controls.currentStep / controls.totalSteps) * 100;
  };

  if (!interactive) {
    return null;
  }

  return (
    <Card className={`border-0 bg-gray-50 ${className}`}>
      <CardContent className="p-4">
        <div className="space-y-4">
          {/* Progress Bar */}
          {controls.totalSteps > 0 && (
            <div className="space-y-2">
              <div className="flex items-center justify-between text-sm text-gray-600">
                <span>Progress</span>
                <span>{controls.currentStep} / {controls.totalSteps}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  className="bg-blue-500 h-2 rounded-full"
                  initial={{ width: 0 }}
                  animate={{ width: `${getProgressPercentage()}%` }}
                  transition={{ duration: 0.3 }}
                />
              </div>
            </div>
          )}

          {/* Main Controls */}
          <div className="flex items-center justify-center space-x-2">
            {/* Step Backward */}
            {controls.canStepThrough && onStepBackward && (
              <Button
                variant="outline"
                size="sm"
                onClick={onStepBackward}
                disabled={controls.currentStep === 0}
                className="w-10 h-10 p-0"
              >
                <SkipBack className="w-4 h-4" />
              </Button>
            )}

            {/* Play/Pause */}
            <Button
              variant={controls.isPlaying ? "secondary" : "default"}
              size="sm"
              onClick={controls.isPlaying ? onPause : onPlay}
              className="w-12 h-10"
            >
              {controls.isPlaying ? (
                <Pause className="w-5 h-5" />
              ) : (
                <Play className="w-5 h-5" />
              )}
            </Button>

            {/* Step Forward */}
            {controls.canStepThrough && onStepForward && (
              <Button
                variant="outline"
                size="sm"
                onClick={onStepForward}
                disabled={controls.currentStep >= controls.totalSteps}
                className="w-10 h-10 p-0"
              >
                <SkipForward className="w-4 h-4" />
              </Button>
            )}

            {/* Reset */}
            {controls.canReset && (
              <Button
                variant="outline"
                size="sm"
                onClick={onReset}
                className="w-10 h-10 p-0"
              >
                <RotateCcw className="w-4 h-4" />
              </Button>
            )}

            {/* Loop Toggle */}
            {onToggleLoop && (
              <Button
                variant={controls.loop ? "secondary" : "outline"}
                size="sm"
                onClick={onToggleLoop}
                className="w-10 h-10 p-0"
              >
                <Repeat className="w-4 h-4" />
              </Button>
            )}
          </div>

          {/* Speed Control */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Gauge className="w-4 h-4 text-gray-500" />
                <span className="text-sm text-gray-600">Speed</span>
              </div>
              <Badge variant="outline" className="text-xs">
                {getCurrentSpeedLabel()}
              </Badge>
            </div>

            {/* Speed Slider */}
            <div className="px-2">
              <Slider
                value={[controls.speed]}
                onValueChange={(value) => handleSpeedSelect(value[0])}
                min={0.1}
                max={3}
                step={0.1}
                className="w-full"
              />
            </div>

            {/* Speed Presets */}
            <div className="flex items-center justify-center space-x-1">
              {speedOptions.map((option) => (
                <Button
                  key={option.value}
                  variant={controls.speed === option.value ? "secondary" : "ghost"}
                  size="sm"
                  onClick={() => handleSpeedSelect(option.value)}
                  className="h-6 px-2 text-xs"
                >
                  {option.label}
                </Button>
              ))}
            </div>
          </div>

          {/* Advanced Controls */}
          <div className="border-t pt-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="w-full h-8 text-xs"
            >
              <Settings className="w-3 h-3 mr-2" />
              {showAdvanced ? 'Hide' : 'Show'} Advanced Controls
            </Button>

            {showAdvanced && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3 space-y-3"
              >
                {/* Auto-play Toggle */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Auto-play</span>
                  <Button
                    variant={controls.autoPlay ? "secondary" : "outline"}
                    size="sm"
                    onClick={() => {/* Handle auto-play toggle */}}
                    className="h-6 px-3 text-xs"
                  >
                    {controls.autoPlay ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Loop Toggle */}
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Loop</span>
                  <Button
                    variant={controls.loop ? "secondary" : "outline"}
                    size="sm"
                    onClick={onToggleLoop}
                    className="h-6 px-3 text-xs"
                  >
                    {controls.loop ? 'On' : 'Off'}
                  </Button>
                </div>

                {/* Step-through Mode */}
                {controls.canStepThrough && (
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Step Mode</span>
                    <Badge variant="outline" className="text-xs">
                      Available
                    </Badge>
                  </div>
                )}
              </motion.div>
            )}
          </div>

          {/* Status Indicator */}
          <div className="flex items-center justify-center space-x-4 text-xs text-gray-500">
            <div className="flex items-center space-x-1">
              <div className={`w-2 h-2 rounded-full ${
                controls.isPlaying ? 'bg-green-500' : 'bg-gray-400'
              }`} />
              <span>{controls.isPlaying ? 'Playing' : 'Paused'}</span>
            </div>
            
            {controls.totalSteps > 0 && (
              <div className="flex items-center space-x-1">
                <span>Step {controls.currentStep + 1}</span>
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );
};
