/**
 * Mathematics Visualizations Component
 * Interactive mathematical visualizations for various math concepts
 */

import React, { useState, useEffect, useCallback } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { 
  Calculator, 
  TrendingUp, 
  RotateCcw,
  Play,
  Pause,
  Settings
} from "lucide-react";
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { MathVisualizationProps } from '../../../types';

interface MathVisualizationState {
  isPlaying: boolean;
  currentStep: number;
  parameters: {
    [key: string]: number;
  };
}

export const MathVisualizations: React.FC<MathVisualizationProps> = ({
  topic,
  gradeLevel,
  difficulty = 'intermediate',
  mathType,
  showGrid = true,
  showAxes = true,
  showDerivative = false,
  showIntegral = false,
  animateTransformation = true,
  parameters = {},
  onParameterChange,
  className = ''
}) => {
  const [state, setState] = useState<MathVisualizationState>({
    isPlaying: false,
    currentStep: 0,
    parameters: {
      amplitude: 1,
      frequency: 1,
      phase: 0,
      xMin: -10,
      xMax: 10,
      ...parameters
    }
  });

  const [chartData, setChartData] = useState<Array<{x: number, y: number, derivative?: number}>>([]);

  // Generate chart data based on math type and parameters
  const generateChartData = useCallback(() => {
    const { amplitude, frequency, phase, xMin, xMax } = state.parameters;
    const points = [];
    const step = (xMax - xMin) / 200;

    for (let x = xMin; x <= xMax; x += step) {
      let y = 0;
      let derivative = 0;

      switch (mathType) {
        case 'trigonometry':
          y = amplitude * Math.sin(frequency * x + phase);
          derivative = amplitude * frequency * Math.cos(frequency * x + phase);
          break;
        case 'algebra':
          // Quadratic function
          y = amplitude * (x * x) + frequency * x + phase;
          derivative = 2 * amplitude * x + frequency;
          break;
        case 'calculus':
          // Polynomial function
          y = amplitude * Math.pow(x, 3) + frequency * Math.pow(x, 2) + phase * x;
          derivative = 3 * amplitude * Math.pow(x, 2) + 2 * frequency * x + phase;
          break;
        case 'geometry':
          // Circle equation
          if (Math.abs(x) <= amplitude) {
            y = Math.sqrt(amplitude * amplitude - x * x);
            derivative = -x / Math.sqrt(amplitude * amplitude - x * x);
          }
          break;
        default:
          y = amplitude * x + phase;
          derivative = amplitude;
      }

      points.push({
        x: parseFloat(x.toFixed(2)),
        y: parseFloat(y.toFixed(2)),
        derivative: showDerivative ? parseFloat(derivative.toFixed(2)) : undefined
      });
    }

    setChartData(points);
  }, [mathType, state.parameters, showDerivative]);

  // Update chart when parameters change
  useEffect(() => {
    generateChartData();
  }, [generateChartData]);

  // Handle parameter changes
  const handleParameterChange = (key: string, value: number) => {
    setState(prev => ({
      ...prev,
      parameters: {
        ...prev.parameters,
        [key]: value
      }
    }));

    if (onParameterChange) {
      onParameterChange(key, value);
    }
  };

  // Animation control
  const toggleAnimation = () => {
    setState(prev => ({ ...prev, isPlaying: !prev.isPlaying }));
  };

  const resetParameters = () => {
    setState(prev => ({
      ...prev,
      parameters: {
        amplitude: 1,
        frequency: 1,
        phase: 0,
        xMin: -10,
        xMax: 10
      }
    }));
  };

  // Get grade-appropriate parameter ranges
  const getParameterRanges = () => {
    const ranges = {
      elementary: {
        amplitude: { min: 0.5, max: 3, step: 0.5 },
        frequency: { min: 0.5, max: 2, step: 0.5 },
        phase: { min: -2, max: 2, step: 0.5 }
      },
      middle: {
        amplitude: { min: 0.1, max: 5, step: 0.1 },
        frequency: { min: 0.1, max: 3, step: 0.1 },
        phase: { min: -5, max: 5, step: 0.1 }
      },
      high: {
        amplitude: { min: 0.1, max: 10, step: 0.1 },
        frequency: { min: 0.1, max: 5, step: 0.1 },
        phase: { min: -10, max: 10, step: 0.1 }
      },
      college: {
        amplitude: { min: 0.01, max: 20, step: 0.01 },
        frequency: { min: 0.01, max: 10, step: 0.01 },
        phase: { min: -20, max: 20, step: 0.01 }
      }
    };

    return ranges[gradeLevel] || ranges.high;
  };

  const ranges = getParameterRanges();

  const getMathTypeTitle = () => {
    const titles = {
      calculus: 'Calculus Functions',
      algebra: 'Algebraic Functions',
      geometry: 'Geometric Shapes',
      trigonometry: 'Trigonometric Functions',
      statistics: 'Statistical Distributions',
      'linear-algebra': 'Linear Transformations'
    };
    return titles[mathType] || 'Mathematical Function';
  };

  const getMathTypeDescription = () => {
    const descriptions = {
      calculus: 'Explore derivatives, integrals, and function behavior',
      algebra: 'Visualize quadratic and polynomial functions',
      geometry: 'Interactive geometric shapes and transformations',
      trigonometry: 'Sine, cosine, and other trigonometric functions',
      statistics: 'Probability distributions and statistical concepts',
      'linear-algebra': 'Matrix operations and vector transformations'
    };
    return descriptions[mathType] || 'Interactive mathematical visualization';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Calculator className="w-5 h-5 text-blue-500" />
              <div>
                <CardTitle className="text-lg">{getMathTypeTitle()}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">{getMathTypeDescription()}</p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{gradeLevel}</Badge>
              <Badge variant="outline">{difficulty}</Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Main Visualization */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Chart */}
            <div className="h-80 w-full">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  {showGrid && <CartesianGrid strokeDasharray="3 3" />}
                  {showAxes && <XAxis dataKey="x" />}
                  {showAxes && <YAxis />}
                  <Tooltip 
                    formatter={(value, name) => [
                      typeof value === 'number' ? value.toFixed(3) : value, 
                      name === 'y' ? 'f(x)' : name === 'derivative' ? "f'(x)" : name
                    ]}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="y" 
                    stroke="#3b82f6" 
                    strokeWidth={2}
                    dot={false}
                    name="Function"
                  />
                  {showDerivative && (
                    <Line 
                      type="monotone" 
                      dataKey="derivative" 
                      stroke="#ef4444" 
                      strokeWidth={2}
                      strokeDasharray="5 5"
                      dot={false}
                      name="Derivative"
                    />
                  )}
                </LineChart>
              </ResponsiveContainer>
            </div>

            {/* Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {/* Amplitude Control */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Amplitude</label>
                  <span className="text-sm text-gray-600">{state.parameters.amplitude}</span>
                </div>
                <Slider
                  value={[state.parameters.amplitude]}
                  onValueChange={(value) => handleParameterChange('amplitude', value[0])}
                  min={ranges.amplitude.min}
                  max={ranges.amplitude.max}
                  step={ranges.amplitude.step}
                  className="w-full"
                />
              </div>

              {/* Frequency Control */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Frequency</label>
                  <span className="text-sm text-gray-600">{state.parameters.frequency}</span>
                </div>
                <Slider
                  value={[state.parameters.frequency]}
                  onValueChange={(value) => handleParameterChange('frequency', value[0])}
                  min={ranges.frequency.min}
                  max={ranges.frequency.max}
                  step={ranges.frequency.step}
                  className="w-full"
                />
              </div>

              {/* Phase Control */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Phase</label>
                  <span className="text-sm text-gray-600">{state.parameters.phase}</span>
                </div>
                <Slider
                  value={[state.parameters.phase]}
                  onValueChange={(value) => handleParameterChange('phase', value[0])}
                  min={ranges.phase.min}
                  max={ranges.phase.max}
                  step={ranges.phase.step}
                  className="w-full"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-2 pt-4 border-t">
              <Button
                variant={state.isPlaying ? "secondary" : "default"}
                onClick={toggleAnimation}
                className="flex items-center space-x-2"
              >
                {state.isPlaying ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{state.isPlaying ? 'Pause' : 'Animate'}</span>
              </Button>
              
              <Button variant="outline" onClick={resetParameters}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset
              </Button>

              {gradeLevel !== 'elementary' && (
                <Button 
                  variant="outline"
                  onClick={() => {/* Toggle advanced options */}}
                >
                  <Settings className="w-4 h-4 mr-2" />
                  Options
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Learning Points */}
      <Card className="border-0 bg-blue-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <TrendingUp className="w-5 h-5 text-blue-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-blue-900 mb-2">Key Observations</h4>
              <ul className="text-sm text-blue-800 space-y-1">
                <li>• Adjust the amplitude to see how it affects the function's height</li>
                <li>• Change the frequency to observe how it impacts the function's period</li>
                <li>• Modify the phase to see horizontal shifts in the function</li>
                {showDerivative && <li>• The red dashed line shows the derivative (rate of change)</li>}
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
