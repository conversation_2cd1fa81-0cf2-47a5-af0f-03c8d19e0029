/**
 * <PERSON><PERSON> Component - Redesigned
 * Modern, engaging interface for starting new tutoring sessions
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  GraduationCap,
  Sparkles,
  ArrowRight,
  BookOpen,
  Brain,
  Target,
  Zap,
  Users,
  Globe,
  Lightbulb,
  Rocket,
  Star,
  TrendingUp,
  Award,
  Play,
  ChevronRight,
  Search,
  Wand2
} from "lucide-react";
import { EDUCATION_LEVELS, TOPIC_SUGGESTIONS, TOPIC_CATEGORIES } from '../constants';
import { EducationLevel } from '../types';

interface TutorHeroProps {
  onStartSession: (topic: string, educationLevel: EducationLevel) => void;
  isLoading: boolean;
}

export function TutorHero({ onStartSession, isLoading }: TutorHeroProps) {
  const [topic, setTopic] = useState('');
  const [educationLevel, setEducationLevel] = useState<EducationLevel>('high-school');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [currentStep, setCurrentStep] = useState(0);
  const [showOnboarding, setShowOnboarding] = useState(false);
  const [animatedText, setAnimatedText] = useState('');
  const [isTyping, setIsTyping] = useState(false);

  const welcomeTexts = [
    "Ready to learn something amazing?",
    "What sparks your curiosity today?",
    "Let's explore knowledge together!",
    "Your learning journey starts here!"
  ];

  // Animated welcome text
  useEffect(() => {
    const text = welcomeTexts[Math.floor(Math.random() * welcomeTexts.length)];
    setIsTyping(true);
    setAnimatedText('');

    let i = 0;
    const typeInterval = setInterval(() => {
      if (i < text.length) {
        setAnimatedText(text.slice(0, i + 1));
        i++;
      } else {
        setIsTyping(false);
        clearInterval(typeInterval);
      }
    }, 50);

    return () => clearInterval(typeInterval);
  }, []);

  const handleStartSession = () => {
    if (!topic.trim()) return;
    onStartSession(topic.trim(), educationLevel);
  };

  const handleSuggestionClick = (suggestion: string) => {
    setTopic(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && topic.trim()) {
      handleStartSession();
    }
  };

  // Filter suggestions by category
  const filteredSuggestions = selectedCategory
    ? TOPIC_SUGGESTIONS.filter(s => s.category.toLowerCase() === selectedCategory.toLowerCase())
    : TOPIC_SUGGESTIONS.slice(0, 12); // Show first 12 if no category selected

  return (
    <div className="max-w-6xl mx-auto">
      {/* Enhanced Hero Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="text-center mb-16 relative"
      >
        {/* Floating Elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <motion.div
            animate={{
              y: [0, -20, 0],
              rotate: [0, 5, 0]
            }}
            transition={{ duration: 4, repeat: Infinity, ease: "easeInOut" }}
            className="absolute top-10 left-10 w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center"
          >
            <Lightbulb className="w-8 h-8 text-blue-500" />
          </motion.div>
          <motion.div
            animate={{
              y: [0, 15, 0],
              rotate: [0, -5, 0]
            }}
            transition={{ duration: 3, repeat: Infinity, ease: "easeInOut", delay: 1 }}
            className="absolute top-20 right-20 w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center"
          >
            <Star className="w-6 h-6 text-purple-500" />
          </motion.div>
          <motion.div
            animate={{
              y: [0, -10, 0],
              x: [0, 10, 0]
            }}
            transition={{ duration: 5, repeat: Infinity, ease: "easeInOut", delay: 2 }}
            className="absolute bottom-20 left-20 w-14 h-14 bg-green-100 rounded-full flex items-center justify-center"
          >
            <Rocket className="w-7 h-7 text-green-500" />
          </motion.div>
        </div>

        {/* Main Hero Content */}
        <div className="relative z-10">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="flex items-center justify-center mb-8"
          >
            <div className="relative">
              <div className="flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-3xl shadow-2xl">
                <GraduationCap className="w-12 h-12 text-white" />
              </div>
              <motion.div
                animate={{ rotate: 360 }}
                transition={{ duration: 20, repeat: Infinity, ease: "linear" }}
                className="absolute -inset-2 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 rounded-3xl opacity-20 blur-sm"
              />
            </div>
          </motion.div>

          <motion.h1
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.4 }}
            className="text-5xl md:text-7xl font-bold text-gray-900 mb-6 leading-tight"
          >
            Your Personal{" "}
            <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 bg-clip-text text-transparent">
              AI Tutor
            </span>
          </motion.h1>

          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.6 }}
            className="text-2xl text-gray-600 mb-4 h-8 flex items-center justify-center"
          >
            {animatedText}
            {isTyping && <span className="ml-1 animate-pulse">|</span>}
          </motion.div>

          <motion.p
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="text-lg text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed"
          >
            Experience personalized learning like never before. Get explanations tailored to your level,
            interactive visualizations, and real-time feedback to accelerate your understanding.
          </motion.p>

          {/* Enhanced Features Grid */}
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 1 }}
            className="grid grid-cols-2 md:grid-cols-4 gap-6 mb-12"
          >
            {[
              { icon: Brain, label: "AI-Powered", color: "blue", description: "Smart explanations" },
              { icon: Target, label: "Adaptive", color: "green", description: "Your level" },
              { icon: Zap, label: "Interactive", color: "yellow", description: "Hands-on learning" },
              { icon: TrendingUp, label: "Progressive", color: "purple", description: "Track growth" }
            ].map((feature, index) => (
              <motion.div
                key={feature.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 1.2 + index * 0.1 }}
                whileHover={{ scale: 1.05, y: -5 }}
                className="group"
              >
                <Card className="p-6 h-full border-0 shadow-lg bg-gradient-to-br from-white to-gray-50 hover:shadow-xl transition-all duration-300">
                  <div className="flex flex-col items-center text-center space-y-3">
                    <div className={`w-12 h-12 rounded-2xl bg-${feature.color}-100 flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
                      <feature.icon className={`w-6 h-6 text-${feature.color}-600`} />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900 mb-1">{feature.label}</h3>
                      <p className="text-sm text-gray-600">{feature.description}</p>
                    </div>
                  </div>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </motion.div>

      {/* Enhanced Input Section */}
      <motion.div
        initial={{ opacity: 0, y: 30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 1.4 }}
      >
        <Card className="mb-12 shadow-2xl border-0 bg-gradient-to-br from-white via-blue-50/30 to-purple-50/30 backdrop-blur-sm">
          <CardHeader className="text-center pb-6">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.4, delay: 1.6 }}
            >
              <CardTitle className="flex items-center justify-center space-x-3 text-2xl">
                <div className="w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl flex items-center justify-center">
                  <Wand2 className="w-5 h-5 text-white" />
                </div>
                <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Start Your Learning Journey
                </span>
              </CardTitle>
            </motion.div>
            <p className="text-gray-600 mt-2">Tell us what you want to learn, and we'll create a personalized experience just for you</p>
          </CardHeader>

          <CardContent className="space-y-8 px-8 pb-8">
            {/* Interactive Topic Input */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 1.8 }}
              className="space-y-4"
            >
              <Label htmlFor="topic" className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                <Search className="w-5 h-5 text-blue-500" />
                <span>What would you like to learn about?</span>
              </Label>
              <div className="relative">
                <Input
                  id="topic"
                  placeholder="Try: 'Explain photosynthesis', 'How do neural networks work?', 'Ancient Roman history'..."
                  value={topic}
                  onChange={(e) => setTopic(e.target.value)}
                  onKeyPress={handleKeyPress}
                  className="text-lg py-4 pl-12 pr-4 border-2 border-gray-200 focus:border-blue-400 rounded-xl shadow-sm"
                  disabled={isLoading}
                />
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" />
                {topic && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0 }}
                    animate={{ opacity: 1, scale: 1 }}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2"
                  >
                    <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                  </motion.div>
                )}
              </div>
            </motion.div>

            {/* Enhanced Education Level Selection */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.4, delay: 2 }}
              className="space-y-4"
            >
              <Label className="text-lg font-semibold text-gray-800 flex items-center space-x-2">
                <Award className="w-5 h-5 text-purple-500" />
                <span>Your Education Level</span>
              </Label>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3">
                {EDUCATION_LEVELS.map((level) => (
                  <motion.div
                    key={level.id}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => setEducationLevel(level.id)}
                    className={`p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 ${
                      educationLevel === level.id
                        ? 'border-blue-500 bg-blue-50 shadow-lg'
                        : 'border-gray-200 bg-white hover:border-blue-300 hover:shadow-md'
                    }`}
                  >
                    <div className="flex flex-col items-center text-center space-y-2">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        educationLevel === level.id ? 'bg-blue-500' : 'bg-gray-100'
                      }`}>
                        <level.icon className={`w-5 h-5 ${
                          educationLevel === level.id ? 'text-white' : 'text-gray-600'
                        }`} />
                      </div>
                      <div>
                        <div className={`font-medium ${
                          educationLevel === level.id ? 'text-blue-700' : 'text-gray-900'
                        }`}>
                          {level.name}
                        </div>
                        <div className="text-xs text-gray-500">{level.ageRange}</div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            {/* Enhanced Start Button */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 2.2 }}
              className="pt-4"
            >
              <Button
                onClick={handleStartSession}
                disabled={!topic.trim() || isLoading}
                className="w-full text-xl py-6 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500 hover:from-blue-600 hover:via-purple-600 hover:to-pink-600 shadow-xl hover:shadow-2xl transform hover:scale-[1.02] transition-all duration-300 rounded-xl"
                size="lg"
              >
                {isLoading ? (
                  <motion.div
                    animate={{ rotate: 360 }}
                    transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    className="flex items-center"
                  >
                    <div className="w-6 h-6 border-3 border-white border-t-transparent rounded-full mr-3" />
                    <span>Creating Your Learning Experience...</span>
                  </motion.div>
                ) : (
                  <motion.div
                    whileHover={{ x: 5 }}
                    className="flex items-center justify-center"
                  >
                    <Play className="w-6 h-6 mr-3" />
                    <span>Start Learning Adventure</span>
                    <ArrowRight className="w-6 h-6 ml-3" />
                  </motion.div>
                )}
              </Button>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Enhanced Topic Suggestions */}
      <motion.div
        initial={{ opacity: 0, y: 40 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 2.4 }}
      >
        <Card className="shadow-2xl border-0 bg-gradient-to-br from-white via-green-50/30 to-blue-50/30">
          <CardHeader className="text-center pb-6">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.4, delay: 2.6 }}
            >
              <CardTitle className="flex items-center justify-center space-x-3 text-2xl">
                <div className="w-10 h-10 bg-gradient-to-r from-green-500 to-blue-500 rounded-xl flex items-center justify-center">
                  <Lightbulb className="w-5 h-5 text-white" />
                </div>
                <span className="bg-gradient-to-r from-green-600 to-blue-600 bg-clip-text text-transparent">
                  Popular Learning Topics
                </span>
              </CardTitle>
            </motion.div>
            <p className="text-gray-600 mt-2">Get inspired by what other students are exploring</p>
          </CardHeader>

          <CardContent className="px-8 pb-8">
            {/* Enhanced Category Filter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 2.8 }}
              className="flex flex-wrap gap-3 mb-8 justify-center"
            >
              <motion.div whileHover={{ scale: 1.05 }} whileTap={{ scale: 0.95 }}>
                <Button
                  variant={selectedCategory === null ? "default" : "outline"}
                  size="lg"
                  onClick={() => setSelectedCategory(null)}
                  className={`rounded-full px-6 py-2 transition-all duration-300 ${
                    selectedCategory === null
                      ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg'
                      : 'hover:shadow-md'
                  }`}
                >
                  <Globe className="w-4 h-4 mr-2" />
                  All Topics
                </Button>
              </motion.div>

              {TOPIC_CATEGORIES.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 3 + index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Button
                    variant={selectedCategory === category.id ? "default" : "outline"}
                    size="lg"
                    onClick={() => setSelectedCategory(category.id)}
                    className={`rounded-full px-6 py-2 transition-all duration-300 ${
                      selectedCategory === category.id
                        ? 'bg-gradient-to-r from-blue-500 to-purple-500 shadow-lg'
                        : 'hover:shadow-md'
                    }`}
                  >
                    <category.icon className="w-4 h-4 mr-2" />
                    <span>{category.name}</span>
                  </Button>
                </motion.div>
              ))}
            </motion.div>

            {/* Enhanced Suggestions Grid */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.6, delay: 3.2 }}
              className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6"
            >
              <AnimatePresence mode="wait">
                {filteredSuggestions.map((suggestion, index) => (
                  <motion.div
                    key={suggestion.id}
                    initial={{ opacity: 0, y: 20, scale: 0.9 }}
                    animate={{ opacity: 1, y: 0, scale: 1 }}
                    exit={{ opacity: 0, y: -20, scale: 0.9 }}
                    transition={{ duration: 0.3, delay: index * 0.05 }}
                    whileHover={{
                      scale: 1.03,
                      y: -5,
                      transition: { duration: 0.2 }
                    }}
                    whileTap={{ scale: 0.98 }}
                    onClick={() => handleSuggestionClick(suggestion.name)}
                    className="group cursor-pointer"
                  >
                    <Card className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-br from-white to-gray-50/50 overflow-hidden">
                      <CardContent className="p-6">
                        <div className="flex items-start space-x-4">
                          <motion.div
                            whileHover={{ rotate: 10, scale: 1.1 }}
                            className="flex-shrink-0 w-12 h-12 bg-gradient-to-br from-blue-100 to-purple-100 rounded-xl flex items-center justify-center text-2xl group-hover:shadow-md transition-all duration-300"
                          >
                            {suggestion.icon}
                          </motion.div>

                          <div className="flex-1 min-w-0">
                            <h3 className="font-semibold text-gray-900 group-hover:text-blue-700 transition-colors duration-300 mb-2 line-clamp-2">
                              {suggestion.name}
                            </h3>
                            <p className="text-sm text-gray-600 mb-3 line-clamp-2 leading-relaxed">
                              {suggestion.description}
                            </p>

                            <div className="flex items-center justify-between">
                              <div className="flex items-center space-x-2">
                                <Badge
                                  variant="secondary"
                                  className="text-xs bg-blue-100 text-blue-700 hover:bg-blue-200 transition-colors"
                                >
                                  {suggestion.difficulty}
                                </Badge>
                                <span className="text-xs text-gray-500 flex items-center">
                                  <span className="w-1 h-1 bg-gray-400 rounded-full mr-1" />
                                  {suggestion.estimatedTime}
                                </span>
                              </div>

                              <motion.div
                                initial={{ opacity: 0, x: -10 }}
                                whileHover={{ opacity: 1, x: 0 }}
                                className="text-blue-500"
                              >
                                <ChevronRight className="w-4 h-4" />
                              </motion.div>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </AnimatePresence>
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
