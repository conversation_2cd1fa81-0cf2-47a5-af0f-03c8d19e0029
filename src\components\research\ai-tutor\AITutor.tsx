/**
 * AI Tutor - Main Component
 * Provides personalized AI tutoring experiences for research and learning
 */

import React, { useEffect } from 'react';
import { useTutorStore } from './stores/tutor.store';
import { TutorHero } from './components/TutorHero';
import { TutorChat } from './components/TutorChat';
import { TutorHistory } from './components/TutorHistory';
import { TutorSettings } from './components/TutorSettings';
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { 
  GraduationCap, 
  History, 
  Settings, 
  Home,
  AlertCircle,
  Loader2
} from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from 'sonner';

export function AITutor() {
  const {
    currentView,
    currentSession,
    isSessionActive,
    isLoading,
    error,
    sessions,
    settings,
    startSession,
    sendMessage,
    endSession,
    loadSession,
    deleteSession,
    updateSettings,
    setCurrentView,
    clearError
  } = useTutorStore();

  // Clear any errors when component mounts
  useEffect(() => {
    if (error) {
      clearError();
    }
  }, []);

  // Handle starting a new tutoring session
  const handleStartSession = async (topic: string, educationLevel: any) => {
    try {
      await startSession(topic, educationLevel);
    } catch (error) {
      console.error('Failed to start session:', error);
      toast.error('Failed to start tutoring session');
    }
  };

  // Handle sending a message in the current session
  const handleSendMessage = async (message: string) => {
    try {
      await sendMessage(message);
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
    }
  };

  // Handle ending the current session
  const handleEndSession = () => {
    endSession();
  };

  // Handle loading a previous session
  const handleLoadSession = async (sessionId: string) => {
    try {
      await loadSession(sessionId);
    } catch (error) {
      console.error('Failed to load session:', error);
      toast.error('Failed to load session');
    }
  };

  // Handle deleting a session
  const handleDeleteSession = async (sessionId: string) => {
    try {
      await deleteSession(sessionId);
    } catch (error) {
      console.error('Failed to delete session:', error);
      toast.error('Failed to delete session');
    }
  };

  // Handle updating settings
  const handleUpdateSettings = (newSettings: any) => {
    updateSettings(newSettings);
  };

  // Navigation items
  const navigationItems = [
    { 
      id: 'hero', 
      label: 'Start Learning', 
      icon: Home, 
      disabled: false 
    },
    { 
      id: 'history', 
      label: 'Session History', 
      icon: History, 
      disabled: false,
      badge: sessions.length > 0 ? sessions.length : undefined
    },
    { 
      id: 'settings', 
      label: 'Settings', 
      icon: Settings, 
      disabled: false 
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200 shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Title */}
            <div className="flex items-center space-x-3">
              <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg">
                <GraduationCap className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-xl font-bold text-gray-900">AI Tutor</h1>
                <p className="text-sm text-gray-500">Personalized Learning Assistant</p>
              </div>
            </div>

            {/* Navigation */}
            <div className="flex items-center space-x-2">
              {navigationItems.map((item) => (
                <Button
                  key={item.id}
                  variant={currentView === item.id ? "default" : "ghost"}
                  size="sm"
                  onClick={() => setCurrentView(item.id as any)}
                  disabled={item.disabled}
                  className="relative"
                >
                  <item.icon className="w-4 h-4 mr-2" />
                  {item.label}
                  {item.badge && (
                    <span className="absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                      {item.badge}
                    </span>
                  )}
                </Button>
              ))}
            </div>

            {/* Session Status */}
            {isSessionActive && currentSession && (
              <div className="flex items-center space-x-2">
                <div className="flex items-center space-x-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm">
                  <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                  <span>Active Session</span>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleEndSession}
                  className="text-red-600 hover:text-red-700"
                >
                  End Session
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {error}
              <Button
                variant="ghost"
                size="sm"
                onClick={clearError}
                className="ml-2 h-auto p-0 text-red-600 hover:text-red-700"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        </div>
      )}

      {/* Loading Overlay */}
      {isLoading && (
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-4">
          <Card>
            <CardContent className="flex items-center justify-center py-8">
              <div className="flex items-center space-x-3">
                <Loader2 className="w-6 h-6 animate-spin text-blue-500" />
                <span className="text-gray-600">Loading...</span>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {currentView === 'hero' && !isLoading && (
          <TutorHero
            onStartSession={handleStartSession}
            isLoading={isLoading}
          />
        )}

        {currentView === 'chat' && currentSession && !isLoading && (
          <TutorChat
            session={currentSession}
            onSendMessage={handleSendMessage}
            onEndSession={handleEndSession}
            isLoading={isLoading}
            isStreaming={false} // TODO: Implement streaming
          />
        )}

        {currentView === 'history' && !isLoading && (
          <TutorHistory
            sessions={sessions}
            onSessionSelect={(session) => handleLoadSession(session.id)}
            onSessionDelete={handleDeleteSession}
            onCreateNew={() => setCurrentView('hero')}
          />
        )}

        {currentView === 'settings' && !isLoading && (
          <TutorSettings
            settings={settings}
            onUpdateSettings={handleUpdateSettings}
            onBack={() => setCurrentView('hero')}
          />
        )}
      </div>

      {/* Footer */}
      <div className="bg-white border-t border-gray-200 mt-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-500">
              AI Tutor - Personalized learning powered by advanced AI
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-500">
              <span>Sessions: {sessions.length}</span>
              {isSessionActive && (
                <span className="text-green-600">• Active</span>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
