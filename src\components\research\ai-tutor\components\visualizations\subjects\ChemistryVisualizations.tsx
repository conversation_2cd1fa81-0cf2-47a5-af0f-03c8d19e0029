/**
 * Chemistry Visualizations Component
 * Interactive 3D molecular visualizations and chemical concepts
 */

import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Slider } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { 
  Atom, 
  RotateCcw,
  Play,
  Pause,
  Zap,
  Eye,
  Settings
} from "lucide-react";
import { ChemistryVisualizationProps } from '../../../types';

interface Atom {
  id: string;
  element: string;
  position: { x: number; y: number; z: number };
  color: string;
  radius: number;
}

interface Bond {
  id: string;
  atom1: string;
  atom2: string;
  type: 'single' | 'double' | 'triple';
  strength: number;
}

interface Molecule {
  name: string;
  atoms: Atom[];
  bonds: Bond[];
  formula: string;
}

export const ChemistryVisualizations: React.FC<ChemistryVisualizationProps> = ({
  topic,
  gradeLevel,
  difficulty = 'intermediate',
  chemistryType,
  show3D = true,
  showElectrons = false,
  showBonds = true,
  enableRotation = true,
  showEnergyLevels = false,
  parameters = {},
  onParameterChange,
  className = ''
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isRotating, setIsRotating] = useState(false);
  const [rotationAngle, setRotationAngle] = useState(0);
  const [selectedMolecule, setSelectedMolecule] = useState('water');
  
  const [state, setState] = useState({
    bondLength: 1.0,
    bondAngle: 109.5,
    electronDensity: 0.5,
    temperature: 298,
    ...parameters
  });

  // Element colors (CPK coloring scheme)
  const elementColors = {
    H: '#FFFFFF',  // Hydrogen - White
    C: '#909090',  // Carbon - Gray
    N: '#3050F8',  // Nitrogen - Blue
    O: '#FF0D0D',  // Oxygen - Red
    F: '#90E050',  // Fluorine - Green
    Cl: '#1FF01F', // Chlorine - Green
    Br: '#A62929', // Bromine - Brown
    I: '#940094',  // Iodine - Purple
    S: '#FFFF30',  // Sulfur - Yellow
    P: '#FF8000'   // Phosphorus - Orange
  };

  // Predefined molecules
  const molecules: { [key: string]: Molecule } = {
    water: {
      name: 'Water',
      formula: 'H₂O',
      atoms: [
        { id: 'O1', element: 'O', position: { x: 0, y: 0, z: 0 }, color: elementColors.O, radius: 20 },
        { id: 'H1', element: 'H', position: { x: -30, y: 20, z: 0 }, color: elementColors.H, radius: 12 },
        { id: 'H2', element: 'H', position: { x: 30, y: 20, z: 0 }, color: elementColors.H, radius: 12 }
      ],
      bonds: [
        { id: 'b1', atom1: 'O1', atom2: 'H1', type: 'single', strength: 1 },
        { id: 'b2', atom1: 'O1', atom2: 'H2', type: 'single', strength: 1 }
      ]
    },
    methane: {
      name: 'Methane',
      formula: 'CH₄',
      atoms: [
        { id: 'C1', element: 'C', position: { x: 0, y: 0, z: 0 }, color: elementColors.C, radius: 18 },
        { id: 'H1', element: 'H', position: { x: -25, y: -25, z: -25 }, color: elementColors.H, radius: 12 },
        { id: 'H2', element: 'H', position: { x: 25, y: -25, z: 25 }, color: elementColors.H, radius: 12 },
        { id: 'H3', element: 'H', position: { x: 25, y: 25, z: -25 }, color: elementColors.H, radius: 12 },
        { id: 'H4', element: 'H', position: { x: -25, y: 25, z: 25 }, color: elementColors.H, radius: 12 }
      ],
      bonds: [
        { id: 'b1', atom1: 'C1', atom2: 'H1', type: 'single', strength: 1 },
        { id: 'b2', atom1: 'C1', atom2: 'H2', type: 'single', strength: 1 },
        { id: 'b3', atom1: 'C1', atom2: 'H3', type: 'single', strength: 1 },
        { id: 'b4', atom1: 'C1', atom2: 'H4', type: 'single', strength: 1 }
      ]
    },
    ammonia: {
      name: 'Ammonia',
      formula: 'NH₃',
      atoms: [
        { id: 'N1', element: 'N', position: { x: 0, y: 0, z: 0 }, color: elementColors.N, radius: 18 },
        { id: 'H1', element: 'H', position: { x: -25, y: 20, z: 0 }, color: elementColors.H, radius: 12 },
        { id: 'H2', element: 'H', position: { x: 25, y: 20, z: 0 }, color: elementColors.H, radius: 12 },
        { id: 'H3', element: 'H', position: { x: 0, y: -15, z: 25 }, color: elementColors.H, radius: 12 }
      ],
      bonds: [
        { id: 'b1', atom1: 'N1', atom2: 'H1', type: 'single', strength: 1 },
        { id: 'b2', atom1: 'N1', atom2: 'H2', type: 'single', strength: 1 },
        { id: 'b3', atom1: 'N1', atom2: 'H3', type: 'single', strength: 1 }
      ]
    }
  };

  // Auto-rotation effect
  useEffect(() => {
    let animationFrame: number;
    
    if (isRotating && enableRotation) {
      const rotate = () => {
        setRotationAngle(prev => (prev + 1) % 360);
        animationFrame = requestAnimationFrame(rotate);
      };
      animationFrame = requestAnimationFrame(rotate);
    }

    return () => {
      if (animationFrame) {
        cancelAnimationFrame(animationFrame);
      }
    };
  }, [isRotating, enableRotation]);

  const handleParameterChange = (key: string, value: number) => {
    setState(prev => ({ ...prev, [key]: value }));
    if (onParameterChange) {
      onParameterChange(key, value);
    }
  };

  const toggleRotation = () => {
    setIsRotating(!isRotating);
  };

  const resetView = () => {
    setRotationAngle(0);
    setIsRotating(false);
  };

  const currentMolecule = molecules[selectedMolecule];

  const renderAtom = (atom: Atom, index: number) => {
    const transform = `
      translate(${atom.position.x}px, ${atom.position.y}px) 
      rotateY(${rotationAngle}deg) 
      translateZ(${atom.position.z}px)
    `;

    return (
      <motion.div
        key={atom.id}
        className="absolute rounded-full border-2 border-white shadow-lg flex items-center justify-center text-white font-bold text-xs"
        style={{
          width: atom.radius * 2,
          height: atom.radius * 2,
          backgroundColor: atom.color,
          transform,
          transformStyle: 'preserve-3d',
          left: '50%',
          top: '50%',
          marginLeft: -atom.radius,
          marginTop: -atom.radius,
        }}
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: index * 0.1 }}
      >
        {atom.element}
      </motion.div>
    );
  };

  const renderBond = (bond: Bond, index: number) => {
    const atom1 = currentMolecule.atoms.find(a => a.id === bond.atom1);
    const atom2 = currentMolecule.atoms.find(a => a.id === bond.atom2);
    
    if (!atom1 || !atom2) return null;

    const dx = atom2.position.x - atom1.position.x;
    const dy = atom2.position.y - atom1.position.y;
    const length = Math.sqrt(dx * dx + dy * dy);
    const angle = Math.atan2(dy, dx) * 180 / Math.PI;

    const bondWidth = bond.type === 'single' ? 3 : bond.type === 'double' ? 5 : 7;

    return (
      <div
        key={bond.id}
        className="absolute bg-gray-600 rounded"
        style={{
          width: length,
          height: bondWidth,
          left: '50%',
          top: '50%',
          marginLeft: atom1.position.x - length / 2,
          marginTop: atom1.position.y - bondWidth / 2,
          transform: `rotate(${angle}deg) rotateY(${rotationAngle}deg)`,
          transformOrigin: 'center',
          transformStyle: 'preserve-3d',
          zIndex: 1
        }}
      />
    );
  };

  const getChemistryTypeTitle = () => {
    const titles = {
      'molecular-structure': 'Molecular Structure',
      'reactions': 'Chemical Reactions',
      'bonding': 'Chemical Bonding',
      'periodic-trends': 'Periodic Trends'
    };
    return titles[chemistryType] || 'Chemistry Visualization';
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Header */}
      <Card className="border-0 shadow-sm">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <Atom className="w-5 h-5 text-purple-500" />
              <div>
                <CardTitle className="text-lg">{getChemistryTypeTitle()}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">
                  Interactive 3D molecular visualization
                </p>
              </div>
            </div>
            <div className="flex items-center space-x-2">
              <Badge variant="outline">{gradeLevel}</Badge>
              <Badge variant="outline">{difficulty}</Badge>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Molecule Selector */}
      <Card className="border-0 shadow-sm">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">Select Molecule</h4>
              <p className="text-sm text-gray-600">Choose a molecule to visualize</p>
            </div>
            <div className="flex space-x-2">
              {Object.entries(molecules).map(([key, molecule]) => (
                <Button
                  key={key}
                  variant={selectedMolecule === key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedMolecule(key)}
                >
                  {molecule.formula}
                </Button>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 3D Visualization */}
      <Card className="border-0 shadow-lg">
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Molecule Info */}
            <div className="text-center">
              <h3 className="text-xl font-semibold">{currentMolecule.name}</h3>
              <p className="text-lg text-gray-600">{currentMolecule.formula}</p>
            </div>

            {/* 3D Viewer */}
            <div 
              ref={containerRef}
              className="relative bg-gradient-to-br from-gray-900 to-gray-700 rounded-lg h-80 overflow-hidden"
              style={{ perspective: '1000px' }}
            >
              {/* Bonds */}
              {showBonds && currentMolecule.bonds.map((bond, index) => 
                renderBond(bond, index)
              )}
              
              {/* Atoms */}
              {currentMolecule.atoms.map((atom, index) => 
                renderAtom(atom, index)
              )}

              {/* Electron clouds (simplified) */}
              {showElectrons && (
                <div className="absolute inset-0 pointer-events-none">
                  {currentMolecule.atoms.map((atom, index) => (
                    <div
                      key={`electron-${atom.id}`}
                      className="absolute rounded-full bg-blue-200 opacity-20"
                      style={{
                        width: atom.radius * 4,
                        height: atom.radius * 4,
                        left: '50%',
                        top: '50%',
                        marginLeft: atom.position.x - atom.radius * 2,
                        marginTop: atom.position.y - atom.radius * 2,
                        transform: `rotateY(${rotationAngle}deg)`,
                      }}
                    />
                  ))}
                </div>
              )}
            </div>

            {/* Controls */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Bond Length */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Bond Length</label>
                  <span className="text-sm text-gray-600">{state.bondLength.toFixed(1)} Å</span>
                </div>
                <Slider
                  value={[state.bondLength]}
                  onValueChange={(value) => handleParameterChange('bondLength', value[0])}
                  min={0.5}
                  max={2.0}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* Bond Angle */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <label className="text-sm font-medium">Bond Angle</label>
                  <span className="text-sm text-gray-600">{state.bondAngle.toFixed(1)}°</span>
                </div>
                <Slider
                  value={[state.bondAngle]}
                  onValueChange={(value) => handleParameterChange('bondAngle', value[0])}
                  min={90}
                  max={180}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>

            {/* Action Buttons */}
            <div className="flex items-center justify-center space-x-2 pt-4 border-t">
              <Button
                variant={isRotating ? "secondary" : "default"}
                onClick={toggleRotation}
                disabled={!enableRotation}
                className="flex items-center space-x-2"
              >
                {isRotating ? <Pause className="w-4 h-4" /> : <Play className="w-4 h-4" />}
                <span>{isRotating ? 'Stop' : 'Rotate'}</span>
              </Button>
              
              <Button variant="outline" onClick={resetView}>
                <RotateCcw className="w-4 h-4 mr-2" />
                Reset View
              </Button>

              <Button 
                variant="outline"
                onClick={() => {/* Toggle options */}}
              >
                <Settings className="w-4 h-4 mr-2" />
                Options
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Molecular Information */}
      <Card className="border-0 bg-purple-50">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <Zap className="w-5 h-5 text-purple-500 mt-0.5" />
            <div>
              <h4 className="font-medium text-purple-900 mb-2">Molecular Properties</h4>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-purple-800">
                <div>
                  <span className="font-medium">Atoms:</span> {currentMolecule.atoms.length}
                </div>
                <div>
                  <span className="font-medium">Bonds:</span> {currentMolecule.bonds.length}
                </div>
                <div>
                  <span className="font-medium">Formula:</span> {currentMolecule.formula}
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};
